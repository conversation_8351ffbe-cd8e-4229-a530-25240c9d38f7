#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ffmpeg路径包装器 - 终极版
确保在任何情况下都能找到FFmpeg
"""

import os
import sys
from pathlib import Path

def setup_ffmpeg_path():
    """设置ffmpeg路径 - 终极版"""
    
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # 打包后的exe运行时
        exe_dir = Path(sys.executable).parent
        if hasattr(sys, '_MEIPASS'):
            internal_dir = Path(sys._MEIPASS)
        else:
            internal_dir = exe_dir / "_internal"
    else:
        # 开发环境
        exe_dir = Path(__file__).parent.parent
        internal_dir = exe_dir
    
    print(f"🔍 EXE目录: {exe_dir}")
    print(f"🔍 内部目录: {internal_dir}")
    
    # 多个可能的FFmpeg位置 - 按优先级排序
    ffmpeg_paths = [
        # 1. EXE同级目录（最高优先级）
        exe_dir / "ffmpeg.exe",
        exe_dir / "ffprobe.exe",
        
        # 2. EXE同级的bin目录
        exe_dir / "bin" / "ffmpeg.exe",
        exe_dir / "bin" / "ffprobe.exe",
        
        # 3. _internal目录中的ffmpeg_bundle
        internal_dir / "ffmpeg_bundle" / "bin" / "ffmpeg.exe",
        internal_dir / "ffmpeg_bundle" / "bin" / "ffprobe.exe",
        
        # 4. 其他可能位置
        exe_dir / "ffmpeg_bundle" / "bin" / "ffmpeg.exe",
        exe_dir / "ffmpeg_bundle" / "bin" / "ffprobe.exe",
    ]
    
    # 查找FFmpeg可执行文件
    ffmpeg_exe = None
    ffprobe_exe = None
    
    print("🔍 搜索FFmpeg文件...")
    
    # 检查成对的ffmpeg和ffprobe
    for i in range(0, len(ffmpeg_paths), 2):
        ffmpeg_path = ffmpeg_paths[i]
        ffprobe_path = ffmpeg_paths[i + 1]
        
        print(f"   检查: {ffmpeg_path}")
        print(f"   检查: {ffprobe_path}")
        
        if ffmpeg_path.exists() and ffprobe_path.exists():
            ffmpeg_exe = str(ffmpeg_path)
            ffprobe_exe = str(ffprobe_path)
            
            # 添加目录到PATH
            bin_dir = ffmpeg_path.parent
            if str(bin_dir) not in os.environ.get("PATH", ""):
                os.environ["PATH"] = str(bin_dir) + os.pathsep + os.environ.get("PATH", "")
            
            print(f"✅ 找到ffmpeg: {ffmpeg_exe}")
            print(f"✅ 找到ffprobe: {ffprobe_exe}")
            break
        else:
            if not ffmpeg_path.exists():
                print(f"❌ 不存在: {ffmpeg_path}")
            if not ffprobe_path.exists():
                print(f"❌ 不存在: {ffprobe_path}")
    
    if not ffmpeg_exe:
        print("⚠️ 未找到ffmpeg，列出实际存在的文件:")
        
        # 列出EXE目录内容
        print(f"\n📁 EXE目录内容 ({exe_dir}):")
        try:
            for item in exe_dir.iterdir():
                if item.is_file() and item.suffix == '.exe':
                    print(f"  📄 {item.name}")
                elif item.is_dir():
                    print(f"  📁 {item.name}/")
        except Exception as e:
            print(f"  ❌ 无法列出目录: {e}")
        
        # 列出_internal目录内容
        if internal_dir.exists() and internal_dir != exe_dir:
            print(f"\n📁 内部目录内容 ({internal_dir}):")
            try:
                for item in internal_dir.iterdir():
                    if item.is_dir():
                        print(f"  📁 {item.name}/")
                        if "ffmpeg" in item.name.lower():
                            print(f"     ⭐ 可能的FFmpeg目录!")
                            try:
                                for subitem in item.iterdir():
                                    print(f"       📁 {subitem.name}")
                            except:
                                pass
            except Exception as e:
                print(f"  ❌ 无法列出目录: {e}")
    
    return ffmpeg_exe, ffprobe_exe

# 自动设置ffmpeg路径
setup_ffmpeg_path()
