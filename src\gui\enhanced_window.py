#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版主窗口界面 - 支持批量操作和直观操作
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import subprocess
import platform
from PIL import Image, ImageTk
import cv2

# 尝试导入拖拽功能
try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    DND_AVAILABLE = True
    print("✅ tkinterdnd2 已加载，拖拽功能可用")
except ImportError as e:
    DND_AVAILABLE = False
    print("⚠️ 提示: tkinterdnd2 未安装或版本不兼容，拖拽功能不可用")
    print("安装命令: pip install tkinterdnd2==0.4.2")
    print("安装后重启软件即可使用完整拖拽功能（支持文件和文件夹拖拽）")
    print(f"错误详情: {e}")
except Exception as e:
    DND_AVAILABLE = False
    print(f"⚠️ 拖拽功能初始化失败: {e}")
    print("建议重新安装: pip uninstall tkinterdnd2 && pip install tkinterdnd2==0.4.2")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.video_deduplication import VideoDeduplicator
from core.timestamp_control import TimestampController
from core.image_to_video import ImageToVideoConverter
from core.ab_video import ABVideoProcessor

class EnhancedVideoEditor:
    def __init__(self, root):
        # 创建功能互斥锁
        self.dedup_lock = threading.Lock()
        self.timestamp_lock = threading.Lock()
        self.image_lock = threading.Lock()
        self.ab_lock = threading.Lock()
        
        # 初始化进度条和状态变量字典
        self.progress_vars = {}
        self.status_vars = {}
        
        # 初始化根窗口 - 优先使用拖拽支持的窗口
        global DND_AVAILABLE

        if DND_AVAILABLE and root is None:
            try:
                from tkinterdnd2 import TkinterDnD
                self.root = TkinterDnD.Tk()
                print("✅ 拖拽功能窗口创建成功")
            except Exception as e:
                print(f"⚠️ 拖拽窗口创建失败: {e}")
                self.root = tk.Tk()
                DND_AVAILABLE = False
        else:
            self.root = root if root else tk.Tk()

        self.root.title("卡妙视频编辑软件 - 增强版")
        self.root.geometry("1500x1000")
        self.root.minsize(1300, 900)

        # 初始化功能模块
        # 视频去重模块初始化
        try:
            self.video_deduplicator = VideoDeduplicator()
            print("✅ 视频去重模块加载成功")
        except Exception as e:
            self.video_deduplicator = None
            print(f"⚠️ 视频去重模块加载失败: {str(e)}")

        # 时间戳控制模块初始化
        try:
            self.timestamp_controller = TimestampController()
            print("✅ 时间戳控制模块加载成功")
        except Exception as e:
            self.timestamp_controller = None
            print(f"⚠️ 时间戳控制模块加载失败: {str(e)}")

        # AB视频处理模块初始化
        try:
            self.ab_processor = ABVideoProcessor()
            print("✅ AB视频处理模块加载成功")
        except Exception as e:
            self.ab_processor = None
            print(f"⚠️ AB视频处理模块加载失败: {str(e)}")
            
        # 图片转视频模块初始化
        try:
            # 检查必要的依赖
            import cv2
            import numpy
            from PIL import Image
            
            # 如果所有依赖都正常，再初始化转换器
            self.image_converter = ImageToVideoConverter()
            print("✅ 图片转视频模块加载成功")
        except ImportError as e:
            self.image_converter = None
            print(f"⚠️ 图片转视频模块加载失败：缺少必要依赖 - {str(e)}")
        except Exception as e:
            self.image_converter = None
            print(f"⚠️ 图片转视频模块加载失败：{str(e)}")

        # 检查各模块状态并打印汇总信息
        modules_status = {
            "视频去重": self.video_deduplicator is not None,
            "时间戳控制": self.timestamp_controller is not None,
            "AB视频处理": self.ab_processor is not None,
            "图片转视频": self.image_converter is not None
        }
        
        print("\n模块加载状态汇总:")
        for module, status in modules_status.items():
            print(f"{'✅' if status else '❌'} {module}: {'已加载' if status else '未加载'}")

        # 文件列表和输出目录
        self.current_files = {}
        self.output_directories = {
            'dedup': '',
            'timestamp': '',
            'images': '',
            'ab': ''
        }

        # 为每个功能创建独立的状态和进度变量
        for feature in ['dedup', 'timestamp', 'images', 'ab']:
            self.progress_vars[feature] = tk.DoubleVar()
            self.status_vars[feature] = tk.StringVar()
            self.status_vars[feature].set("就绪")
            
        self.setup_ui()

    def setup_drag_drop(self, widget, callback):
        """为控件设置拖拽功能 - 修复版"""
        if not DND_AVAILABLE:
            return False

        try:
            # 确保导入成功
            from tkinterdnd2 import DND_FILES

            # 注册拖拽目标
            widget.drop_target_register(DND_FILES)

            # 绑定拖拽事件
            widget.dnd_bind('<<Drop>>', callback)

            # 绑定拖拽进入和离开事件（用于视觉反馈）
            widget.dnd_bind('<<DragEnter>>', lambda e: self._on_drag_enter(widget))
            widget.dnd_bind('<<DragLeave>>', lambda e: self._on_drag_leave(widget))

            print(f"✅ 拖拽功能设置成功: {type(widget).__name__}")
            return True

        except Exception as e:
            print(f"❌ 拖拽设置失败: {e}")
            return False

    def _on_drag_enter(self, widget):
        """拖拽进入时的视觉反馈"""
        try:
            if isinstance(widget, tk.Listbox):
                widget.configure(bg='lightblue')
            elif hasattr(widget, 'configure'):
                widget.configure(bg='lightblue')
        except:
            pass

    def _on_drag_leave(self, widget):
        """拖拽离开时恢复原样"""
        try:
            if isinstance(widget, tk.Listbox):
                widget.configure(bg='white')
            elif hasattr(widget, 'configure'):
                widget.configure(bg='white')
        except:
            pass

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        if hasattr(self, 'canvas'):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_canvas_configure(self, event):
        """处理Canvas大小变化事件，确保内容宽度自适应"""
        # 更新scrollable_frame的宽度以匹配canvas宽度
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def setup_ui(self):
        """设置用户界面"""
        # 创建主容器和滚动条 - 优化布局，移除左侧空白
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Canvas和滚动条 - 紧凑布局
        self.canvas = tk.Canvas(main_container, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # 配置滚动区域
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        # 创建窗口 - 确保内容填满整个宽度
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 绑定Canvas大小变化事件，确保内容宽度自适应
        self.canvas.bind('<Configure>', self._on_canvas_configure)

        # 布局Canvas和滚动条 - 紧凑布局
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.root.bind("<MouseWheel>", self._on_mousewheel)

        # 主框架现在是scrollable_frame
        main_frame = self.scrollable_frame
        
        # 创建标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(title_frame, text="🎬 卡妙视频编辑软件 - 增强版", 
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        subtitle_label = ttk.Label(title_frame, 
                                  text="批量处理 | 四大核心功能 | 一键操作",
                                  font=("Arial", 10))
        subtitle_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 全局设置区域
        global_settings_frame = ttk.LabelFrame(main_frame, text="🔧 全局设置", padding=5)
        global_settings_frame.pack(fill=tk.X, pady=(0, 10))

        # JSON文件生成选项
        json_frame = ttk.Frame(global_settings_frame)
        json_frame.pack(fill=tk.X)

        self.generate_json = tk.BooleanVar(value=False)  # 默认不生成
        json_checkbox = ttk.Checkbutton(json_frame,
                                       text="生成处理信息JSON文件（包含处理参数和结果详情）",
                                       variable=self.generate_json)
        json_checkbox.pack(side=tk.LEFT)

        # JSON说明
        json_help = ttk.Label(json_frame,
                             text="💡 JSON文件用于记录处理参数和结果，便于后续分析和验证",
                             font=("Arial", 8), foreground="gray")
        json_help.pack(side=tk.LEFT, padx=(10, 0))

        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建四个功能标签页
        self.create_deduplication_tab()
        self.create_timestamp_tab()
        self.create_image_to_video_tab()
        self.create_ab_video_tab()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_deduplication_tab(self):
        """创建视频去重标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🎭 视频去重")
        
        # 说明区域
        info_frame = ttk.LabelFrame(frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_text = """通过添加特效、滤镜、转场、镜像等效果来修改视频，绕过平台原创检测系统
支持效果：镜像翻转、颜色滤镜、模糊效果、噪声添加、裁剪调整等"""
        ttk.Label(info_frame, text=info_text, wraplength=800).pack()
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 按钮区域
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="📁 选择视频文件", 
                  command=lambda: self.select_files("dedup", "video")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="📂 选择视频文件夹", 
                  command=lambda: self.select_folder("dedup", "video")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ 清空列表", 
                  command=lambda: self.clear_files("dedup")).pack(side=tk.LEFT, padx=5)
        
        # 文件列表
        self.dedup_listbox = tk.Listbox(file_frame, height=6)
        self.dedup_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # 启用拖拽功能
        if self.setup_drag_drop(self.dedup_listbox, lambda event: self.on_files_dropped(event, "dedup", "video")):
            # 添加拖拽提示
            drag_hint = ttk.Label(file_frame, text="💡 提示: 可直接拖拽视频文件或文件夹到列表区域",
                                 font=("Arial", 9), foreground="blue")
            drag_hint.pack(pady=(2, 0))
        else:
            # 添加安装提示
            install_hint = ttk.Label(file_frame,
                                   text="⚠️ 提示: tkinterdnd2 未安装，拖拽功能不可用，但点击选择功能仍可用\n安装命令: pip install tkinterdnd2",
                                   font=("Arial", 8), foreground="orange", wraplength=600)
            install_hint.pack(pady=(5, 0))

        # 输出目录选择
        self.create_output_directory_frame(frame, "dedup", "输出目录设置")

        # 控制区域
        control_frame = ttk.LabelFrame(frame, text="处理设置", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 效果选择
        effects_frame = ttk.LabelFrame(control_frame, text="效果设置", padding=5)
        effects_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：滤镜和转场
        row1_frame = ttk.Frame(effects_frame)
        row1_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row1_frame, text="滤镜效果:").pack(side=tk.LEFT)
        filter_options = ["none", "blur", "sharpen", "vintage", "sepia", "grayscale", "invert", "brightness",
                         "contrast", "saturation", "hue_shift", "gamma", "exposure", "shadows", "highlights",
                         "vibrance", "warmth", "coolness", "film_grain", "cross_process", "bleach_bypass"]
        self.dedup_filter = ttk.Combobox(row1_frame, values=filter_options, state="readonly", width=15)
        self.dedup_filter.set("none")
        self.dedup_filter.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(row1_frame, text="转场效果:").pack(side=tk.LEFT)
        transition_options = ["none", "fade", "slide", "zoom", "dissolve", "wipe", "push", "cover", "uncover",
                             "reveal", "iris", "radial", "barn_door", "clock", "ripple", "morph"]
        self.dedup_transition = ttk.Combobox(row1_frame, values=transition_options, state="readonly", width=15)
        self.dedup_transition.set("none")
        self.dedup_transition.pack(side=tk.LEFT, padx=(5, 15))

        # 第二行：特效和镜像
        row2_frame = ttk.Frame(effects_frame)
        row2_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row2_frame, text="特效处理:").pack(side=tk.LEFT)
        effect_options = ["none", "noise", "pixelate", "edge_detect", "emboss", "motion_blur", "vignette",
                         "oil_painting", "watercolor", "sketch", "cartoon", "glitch", "datamosh", "chromatic_aberration",
                         "lens_flare", "light_leak", "film_burn", "static", "scan_lines", "crt_effect", "vhs_effect"]
        self.dedup_effect = ttk.Combobox(row2_frame, values=effect_options, state="readonly", width=18)
        self.dedup_effect.set("none")
        self.dedup_effect.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(row2_frame, text="镜像翻转:").pack(side=tk.LEFT)
        self.dedup_mirror = ttk.Combobox(row2_frame, values=["否", "水平翻转", "垂直翻转", "双向翻转"],
                                        state="readonly", width=12)
        self.dedup_mirror.set("否")
        self.dedup_mirror.pack(side=tk.LEFT, padx=(5, 15))

        # 第三行：角度和强度
        row3_frame = ttk.Frame(effects_frame)
        row3_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row3_frame, text="旋转角度:").pack(side=tk.LEFT)
        self.dedup_angle = ttk.Spinbox(row3_frame, from_=-180, to=180, width=8, increment=1)
        self.dedup_angle.set("0")
        self.dedup_angle.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(row3_frame, text="效果强度:").pack(side=tk.LEFT)
        self.dedup_intensity = ttk.Combobox(row3_frame, values=["轻度", "中度", "重度", "极重"],
                                           state="readonly", width=10)
        self.dedup_intensity.set("中度")
        self.dedup_intensity.pack(side=tk.LEFT, padx=(5, 15))

        # 第四行：高级去重方法
        row4_frame = ttk.Frame(effects_frame)
        row4_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row4_frame, text="去重方法:").pack(side=tk.LEFT)
        dedup_methods = ["standard", "frame_skip", "time_stretch", "speed_variation", "reverse_segments",
                        "frame_interpolation", "temporal_shift", "content_aware", "ai_enhancement"]
        self.dedup_method = ttk.Combobox(row4_frame, values=dedup_methods, state="readonly", width=15)
        self.dedup_method.set("standard")
        self.dedup_method.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(row4_frame, text="音频处理:").pack(side=tk.LEFT)
        audio_methods = ["none", "pitch_shift", "speed_change", "reverb", "echo", "distortion", "noise_gate"]
        self.dedup_audio = ttk.Combobox(row4_frame, values=audio_methods, state="readonly", width=12)
        self.dedup_audio.set("none")
        self.dedup_audio.pack(side=tk.LEFT, padx=(5, 15))

        # 效果说明
        effects_help_text = ("💡 效果说明: 滤镜(blur=模糊,vintage=复古,sepia=棕褐色,film_grain=胶片颗粒,cross_process=交叉冲印), "
                           "转场(fade=淡入淡出,slide=滑动,iris=光圈,radial=径向,ripple=波纹), "
                           "特效(noise=噪声,glitch=故障,datamosh=数据损坏,vhs_effect=VHS效果,crt_effect=CRT效果), "
                           "去重方法(frame_skip=跳帧,time_stretch=时间拉伸,ai_enhancement=AI增强), "
                           "音频处理(pitch_shift=变调,reverb=混响,echo=回声)")
        ttk.Label(effects_frame, text=effects_help_text,
                 font=("Arial", 8), foreground="gray", wraplength=800).pack(pady=(5, 0))

        # 设置区域
        settings_frame = ttk.Frame(control_frame)
        settings_frame.pack(fill=tk.X)

        ttk.Label(settings_frame, text="输出帧数:").pack(side=tk.LEFT)
        self.dedup_fps = ttk.Combobox(settings_frame, values=["24", "25", "30", "50", "60"],
                                     state="readonly", width=8)
        self.dedup_fps.set("30")
        self.dedup_fps.pack(side=tk.LEFT, padx=(5, 20))

        # 处理按钮
        ttk.Button(settings_frame, text="🚀 开始批量处理",
                  command=self.process_deduplication,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        
    def create_timestamp_tab(self):
        """创建时间戳控制标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="⏰ 时间戳控制")
        
        # 说明区域
        info_frame = ttk.LabelFrame(frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_text = """实现视频时间轴欺骗：前段时间正常播放，后段快速播完，但播放器显示原始时长
适用于需要在固定时间内播完长视频，但保持原始时长显示的场景"""
        ttk.Label(info_frame, text=info_text, wraplength=800).pack()
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 按钮区域
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="📁 选择视频文件", 
                  command=lambda: self.select_files("timestamp", "video")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="📂 选择视频文件夹", 
                  command=lambda: self.select_folder("timestamp", "video")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ 清空列表", 
                  command=lambda: self.clear_files("timestamp")).pack(side=tk.LEFT, padx=5)
        
        # 文件列表
        self.timestamp_listbox = tk.Listbox(file_frame, height=6)
        self.timestamp_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # 启用拖拽功能
        if self.setup_drag_drop(self.timestamp_listbox, lambda event: self.on_files_dropped(event, "timestamp", "video")):
            # 添加拖拽提示
            drag_hint = ttk.Label(file_frame, text="💡 提示: 可直接拖拽视频文件或文件夹到列表区域",
                                 font=("Arial", 9), foreground="blue")
            drag_hint.pack(pady=(2, 0))
        else:
            # 添加安装提示
            install_hint = ttk.Label(file_frame,
                                   text="⚠️ 提示: tkinterdnd2 未安装，拖拽功能不可用，但点击选择功能仍可用\n安装命令: pip install tkinterdnd2",
                                   font=("Arial", 8), foreground="orange", wraplength=600)
            install_hint.pack(pady=(5, 0))

        # 输出目录选择
        self.create_output_directory_frame(frame, "timestamp", "输出目录设置")

        # 控制区域
        control_frame = ttk.LabelFrame(frame, text="处理设置", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 时间设置
        settings_frame1 = ttk.Frame(control_frame)
        settings_frame1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(settings_frame1, text="正常播放时长(秒):").pack(side=tk.LEFT)
        self.normal_duration = ttk.Spinbox(settings_frame1, from_=10, to=300, width=10, value=30)
        self.normal_duration.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame1, text="显示时长(秒):").pack(side=tk.LEFT)
        self.display_duration = ttk.Spinbox(settings_frame1, from_=10, to=600, width=10, value=60)
        self.display_duration.pack(side=tk.LEFT, padx=(5, 15))

        # 第二行设置
        settings_frame2 = ttk.Frame(control_frame)
        settings_frame2.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(settings_frame2, text="实际播放时长(秒):").pack(side=tk.LEFT)
        self.actual_duration = ttk.Spinbox(settings_frame2, from_=10, to=600, width=10, value=45)
        self.actual_duration.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame2, text="输出帧数:").pack(side=tk.LEFT)
        self.timestamp_fps = ttk.Combobox(settings_frame2, values=["24", "25", "30", "50", "60"],
                                         state="readonly", width=8)
        self.timestamp_fps.set("30")
        self.timestamp_fps.pack(side=tk.LEFT, padx=(5, 15))

        # 自动计算按钮
        ttk.Button(settings_frame2, text="📊 自动计算",
                  command=self.auto_calculate_timestamp,
                  style="TButton").pack(side=tk.LEFT, padx=5)

        # 处理按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(btn_frame, text="🚀 开始批量处理",
                  command=self.process_timestamp,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        
    def create_image_to_video_tab(self):
        """创建图转视频标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🖼️ 图转视频")
        
        # 说明区域
        info_frame = ttk.LabelFrame(frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_text = """将图片序列转换为视频，支持丰富的转场效果和视频方向
支持格式：JPG, PNG, BMP等 | 默认竖屏(9:16)，可选横屏(16:9) | 20+种转场效果"""
        ttk.Label(info_frame, text=info_text, wraplength=800).pack()
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 按钮区域
        btn_frame = ttk.Frame(file_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(btn_frame, text="📁 选择图片文件", 
                  command=lambda: self.select_files("images", "image")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="📂 选择图片文件夹", 
                  command=lambda: self.select_folder("images", "image")).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ 清空列表", 
                  command=lambda: self.clear_files("images")).pack(side=tk.LEFT, padx=5)
        
        # 文件列表
        self.images_listbox = tk.Listbox(file_frame, height=6)
        self.images_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # 启用拖拽功能
        if self.setup_drag_drop(self.images_listbox, lambda event: self.on_files_dropped(event, "images", "image")):
            # 添加拖拽提示
            drag_hint = ttk.Label(file_frame, text="💡 提示: 可直接拖拽图片文件或文件夹到列表区域",
                                 font=("Arial", 9), foreground="blue")
            drag_hint.pack(pady=(2, 0))
        else:
            # 添加安装提示
            install_hint = ttk.Label(file_frame,
                                   text="⚠️ 提示: tkinterdnd2 未安装，拖拽功能不可用，但点击选择功能仍可用\n安装命令: pip install tkinterdnd2",
                                   font=("Arial", 8), foreground="orange", wraplength=600)
            install_hint.pack(pady=(5, 0))

        # 输出目录选择
        self.create_output_directory_frame(frame, "images", "输出目录设置")

        # 控制区域
        control_frame = ttk.LabelFrame(frame, text="处理设置", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 参数设置 - 第一行
        settings_frame1 = ttk.Frame(control_frame)
        settings_frame1.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(settings_frame1, text="每张图片时长(秒):").pack(side=tk.LEFT)
        self.image_duration = ttk.Spinbox(settings_frame1, from_=0.5, to=10, width=8, value=2.0, increment=0.5)
        self.image_duration.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame1, text="视频方向:").pack(side=tk.LEFT)
        self.video_orientation = ttk.Combobox(settings_frame1, values=["竖屏(9:16)", "横屏(16:9)", "正方形(1:1)"],
                                            state="readonly", width=12)
        self.video_orientation.set("竖屏(9:16)")
        self.video_orientation.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame1, text="输出帧数:").pack(side=tk.LEFT)
        self.images_fps = ttk.Combobox(settings_frame1, values=["24", "25", "30", "50", "60"],
                                      state="readonly", width=8)
        self.images_fps.set("30")
        self.images_fps.pack(side=tk.LEFT, padx=(5, 20))

        # 参数设置 - 第二行
        settings_frame2 = ttk.Frame(control_frame)
        settings_frame2.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(settings_frame2, text="转场效果:").pack(side=tk.LEFT)
        transition_options = [
            "random", "fade", "slide_left", "slide_right", "slide_up", "slide_down",
            "zoom_in", "zoom_out", "dissolve", "wipe_left", "wipe_right",
            "wipe_up", "wipe_down", "circle", "diamond", "blinds_horizontal",
            "blinds_vertical", "checkerboard", "spiral", "pixelate", "wave"
        ]
        self.transition_type = ttk.Combobox(settings_frame2, values=transition_options,
                                          state="readonly", width=15)
        self.transition_type.set("random")
        self.transition_type.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame2, text="转场时长(秒):").pack(side=tk.LEFT)
        self.transition_duration = ttk.Spinbox(settings_frame2, from_=0.1, to=3.0, width=8, increment=0.1)
        self.transition_duration.set("0.5")
        self.transition_duration.pack(side=tk.LEFT, padx=(5, 15))

        # 转场效果说明
        transition_help_text = ("💡 转场效果: random=随机转场, fade=淡入淡出, slide_left=左滑, slide_right=右滑, "
                               "slide_up=上滑, slide_down=下滑, zoom_in=放大, zoom_out=缩小, dissolve=溶解, "
                               "wipe_left=左擦除, wipe_right=右擦除, wipe_up=上擦除, wipe_down=下擦除, "
                               "circle=圆形, diamond=菱形, blinds_horizontal=水平百叶窗, blinds_vertical=垂直百叶窗, "
                               "checkerboard=棋盘格, spiral=螺旋, pixelate=像素化, wave=波浪")
        ttk.Label(settings_frame2, text=transition_help_text,
                 font=("Arial", 8), foreground="gray", wraplength=800).pack(side=tk.LEFT, padx=(10, 0))

        # 处理按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, pady=(5, 0))

        # 根据模块状态创建按钮
        if hasattr(self, 'image_converter') and self.image_converter is not None:
            ttk.Button(btn_frame, text="🚀 开始批量处理",
                      command=self.process_images_to_video,
                      style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        else:
            ttk.Label(btn_frame, text="⚠️ 图片转视频模块未正确加载，请检查依赖是否完整安装",
                     font=("Arial", 10), foreground="red").pack(side=tk.LEFT, padx=5)
        
    def create_ab_video_tab(self):
        """创建AB视频标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🔄 AB视频")
        
        # 说明区域
        info_frame = ttk.LabelFrame(frame, text="功能说明", padding=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        # 添加AB视频处理函数
        def process_ab_video(self):
            """处理AB视频"""
            # 检查是否已经在处理中
            if not self.ab_lock.acquire(blocking=False):
                messagebox.showinfo("提示", "AB视频功能正在处理中，请等待当前任务完成")
                return

            # 检查AB视频处理器是否正确初始化
            if not hasattr(self, 'ab_processor') or self.ab_processor is None:
                messagebox.showerror("错误", "AB视频模块未正确初始化，请检查相关依赖是否安装完整")
                self.ab_lock.release()
                return
            if not hasattr(self, 'ab_processor') or self.ab_processor is None:
                messagebox.showerror("错误", "AB视频处理模块未正确初始化，请检查相关依赖是否完整安装")
                return

            if not self.current_a_video or not os.path.exists(self.current_a_video):
                messagebox.showwarning("警告", "请先选择A视频文件")
                return

            if not self.current_b_video or not os.path.exists(self.current_b_video):
                messagebox.showwarning("警告", "请先选择B视频文件")
                return

            try:
                hide_method = self.hide_method.get()
                encode_quality = float(self.encode_quality.get())
                fps = int(self.ab_fps.get())
            except ValueError:
                messagebox.showerror("错误", "请输入有效的参数")
                return

            # 检查输出目录
            output_dir = self.output_directories.get("ab", "")
            if not output_dir:
                output_dir = filedialog.askdirectory(title="选择输出目录")
                if not output_dir:
                    return
                self.set_output_directory("ab", output_dir)

            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(self.current_a_video))[0]
            output_path = os.path.join(output_dir, f"{base_name}_ab.mp4")

            def process_thread():
                try:
                    self.status_var.set("正在处理AB视频...")
                    self.progress_var.set(0)

                    # 调用AB视频处理
                    success = self.ab_processor.create_ab_video(
                        self.current_a_video,
                        self.current_b_video,
                        output_path,
                        hide_method,
                        encode_quality,
                        fps
                    )

                    if success:
                        self.status_var.set("AB视频处理完成！")
                        result = messagebox.askyesno("完成",
                            f"AB视频处理完成！\n输出文件: {output_path}\n\n是否打开输出目录？")
                        if result:
                            self.open_folder(output_dir)
                    else:
                        self.status_var.set("AB视频处理失败")
                        messagebox.showerror("错误", "AB视频处理失败")

                except Exception as e:
                    self.status_var.set(f"处理失败: {str(e)}")
                    messagebox.showerror("错误", f"处理失败: {str(e)}")
                finally:
                    self.progress_var.set(0)

            threading.Thread(target=process_thread, daemon=True).start()

        info_text = """🎯 AB视频功能：创建平台检测为A视频，用户实际观看B视频的混合视频
📋 技术原理：通过元数据覆盖、LSB隐写术、深度隐写等高级技术实现内容替换"""
        ttk.Label(info_frame, text=info_text, wraplength=800, font=("Arial", 10)).pack()

        # 详细使用说明
        usage_frame = ttk.LabelFrame(frame, text="📖 详细使用说明", padding=10)
        usage_frame.pack(fill=tk.X, padx=10, pady=5)

        usage_text = """
🔸 单个AB视频处理：
   1. 选择A视频：平台用于检测的视频文件（可以是任意视频）
   2. 选择B视频：用户实际观看的视频内容（真正想要播放的视频）
   3. 选择嵌入方法：推荐使用deep_steganography或neural_embedding
   4. 设置加密强度：根据安全需求选择basic到quantum_resistant
   5. 点击"创建单个AB视频"开始处理

🔸 批量AB视频处理：
   1. 选择A视频文件夹：包含多个A视频的文件夹
   2. 选择B视频文件夹：包含多个B视频的文件夹
   3. 系统会按文件名排序进行一一对应配对处理
   4. 例如：A文件夹的第1个视频 + B文件夹的第1个视频 = 第1个AB视频

⚠️ 重要提示：
   • A视频和B视频的时长可以不同，系统会自动处理
   • 建议A视频时长不要太短（至少10秒），避免检测异常
   • B视频是实际播放内容，时长和质量按需求选择
   • 输出视频会保持B视频的画质，但包含A视频的元数据特征"""

        usage_label = ttk.Label(usage_frame, text=usage_text, wraplength=900,
                               font=("Arial", 9), justify=tk.LEFT)
        usage_label.pack(anchor=tk.W)
        
        # AB视频选择区域
        ab_frame = ttk.Frame(frame)
        ab_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # A视频区域
        a_frame = ttk.LabelFrame(ab_frame, text="A视频 (平台检测)", padding=5)
        a_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        ttk.Button(a_frame, text="📁 选择A视频",
                  command=self.select_a_video).pack(pady=5)
        self.a_video_label = ttk.Label(a_frame, text="未选择", foreground="gray")
        self.a_video_label.pack(pady=5)

        # A视频拖拽功能
        if self.setup_drag_drop(a_frame, lambda event: self.on_a_video_dropped(event)):
            drag_hint_a = ttk.Label(a_frame, text="💡 可拖拽视频文件",
                                   font=("Arial", 8), foreground="blue")
            drag_hint_a.pack()
        
        # B视频区域
        b_frame = ttk.LabelFrame(ab_frame, text="B视频 (用户观看)", padding=5)
        b_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Button(b_frame, text="📁 选择B视频",
                  command=self.select_b_video).pack(pady=5)
        self.b_video_label = ttk.Label(b_frame, text="未选择", foreground="gray")
        self.b_video_label.pack(pady=5)

        # B视频拖拽功能
        if self.setup_drag_drop(b_frame, lambda event: self.on_b_video_dropped(event)):
            drag_hint_b = ttk.Label(b_frame, text="💡 可拖拽视频文件",
                                   font=("Arial", 8), foreground="blue")
            drag_hint_b.pack()
        
        # 批量AB视频区域
        batch_frame = ttk.LabelFrame(frame, text="批量AB视频处理", padding=10)
        batch_frame.pack(fill=tk.X, padx=10, pady=5)
        
        batch_btn_frame = ttk.Frame(batch_frame)
        batch_btn_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(batch_btn_frame, text="📂 选择A视频文件夹",
                  command=lambda: self.select_folder("ab_a", "video")).pack(side=tk.LEFT, padx=5)
        ttk.Button(batch_btn_frame, text="📂 选择B视频文件夹",
                  command=lambda: self.select_folder("ab_b", "video")).pack(side=tk.LEFT, padx=5)

        # 输出目录选择
        self.create_output_directory_frame(frame, "ab", "输出目录设置")

        # 控制区域
        control_frame = ttk.LabelFrame(frame, text="处理设置", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 嵌入方法
        settings_frame = ttk.Frame(control_frame)
        settings_frame.pack(fill=tk.X)

        ttk.Label(settings_frame, text="嵌入方法:").pack(side=tk.LEFT)
        ab_method_options = [
            "invisible", "metadata", "lsb_steganography", "header_injection", "footer_append",
            "frame_overlay", "audio_channel", "timestamp_shift", "deep_steganography",
            "neural_embedding", "frequency_domain", "wavelet_transform", "dct_embedding",
            "motion_vector_hiding", "subtitle_track", "chapter_markers", "codec_manipulation"
        ]
        self.ab_method = ttk.Combobox(settings_frame, values=ab_method_options,
                                     state="readonly", width=18)
        self.ab_method.set("invisible")
        self.ab_method.pack(side=tk.LEFT, padx=(5, 10))

        # 第二行：高级设置
        settings_frame2 = ttk.Frame(control_frame)
        settings_frame2.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(settings_frame2, text="加密强度:").pack(side=tk.LEFT)
        encryption_levels = ["none", "basic", "advanced", "military", "quantum_resistant"]
        self.ab_encryption = ttk.Combobox(settings_frame2, values=encryption_levels,
                                         state="readonly", width=12)
        self.ab_encryption.set("basic")
        self.ab_encryption.pack(side=tk.LEFT, padx=(5, 15))

        ttk.Label(settings_frame2, text="检测规避:").pack(side=tk.LEFT)
        evasion_methods = ["standard", "adaptive", "ai_powered", "multi_layer", "polymorphic"]
        self.ab_evasion = ttk.Combobox(settings_frame2, values=evasion_methods,
                                      state="readonly", width=12)
        self.ab_evasion.set("standard")
        self.ab_evasion.pack(side=tk.LEFT, padx=(5, 15))

        # 技术参数详细说明
        tech_frame = ttk.LabelFrame(control_frame, text="🔧 技术参数说明", padding=10)
        tech_frame.pack(fill=tk.X, pady=(10, 0))

        # 嵌入方法说明
        method_help_text = ("� 嵌入方法详解:\n"
                          "• invisible=不可见嵌入 (基础级别，适合一般使用)\n"
                          "• deep_steganography=深度隐写 (AI技术，推荐使用)\n"
                          "• neural_embedding=神经网络嵌入 (最高级别，最安全)\n"
                          "• frequency_domain=频域隐藏 (专业级别，抗压缩)\n"
                          "• wavelet_transform=小波变换 (数学算法，高隐蔽性)\n"
                          "• dct_embedding=DCT嵌入 (视频编码级别)\n"
                          "• motion_vector_hiding=运动矢量隐藏 (利用视频运动信息)\n"
                          "• codec_manipulation=编解码器操控 (底层技术)")
        ttk.Label(tech_frame, text=method_help_text,
                 font=("Arial", 9), foreground="darkblue", wraplength=800, justify=tk.LEFT).pack(anchor=tk.W)

        # 安全等级说明
        security_help_text = ("\n🛡️ 安全等级说明:\n"
                            "• none=无加密 (快速处理，无安全保护)\n"
                            "• basic=基础加密 (AES-128，一般安全需求)\n"
                            "• advanced=高级加密 (AES-256，高安全需求)\n"
                            "• military=军用级加密 (多重加密，极高安全)\n"
                            "• quantum_resistant=抗量子加密 (未来安全，最高级别)")
        ttk.Label(tech_frame, text=security_help_text,
                 font=("Arial", 9), foreground="darkgreen", wraplength=800, justify=tk.LEFT).pack(anchor=tk.W)

        # 检测规避说明
        evasion_help_text = ("\n🕵️ 检测规避策略:\n"
                           "• standard=标准规避 (基础反检测)\n"
                           "• adaptive=自适应规避 (动态调整策略)\n"
                           "• ai_powered=AI驱动规避 (智能对抗检测)\n"
                           "• multi_layer=多层规避 (多重防护机制)\n"
                           "• polymorphic=多态规避 (变形技术，最高级别)")
        ttk.Label(tech_frame, text=evasion_help_text,
                 font=("Arial", 9), foreground="darkorange", wraplength=800, justify=tk.LEFT).pack(anchor=tk.W)

        ttk.Label(settings_frame, text="输出帧数:").pack(side=tk.LEFT)
        self.ab_fps = ttk.Combobox(settings_frame, values=["24", "25", "30", "50", "60"],
                                  state="readonly", width=8)
        self.ab_fps.set("30")
        self.ab_fps.pack(side=tk.LEFT, padx=(5, 20))
        
        # 处理按钮
        ttk.Button(settings_frame, text="🎯 创建单个AB视频",
                  command=self.process_single_ab_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_frame, text="🚀 批量AB视频处理",
                  command=self.process_batch_ab_video,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)

        # 常见问题解答
        faq_frame = ttk.LabelFrame(frame, text="❓ 常见问题解答", padding=10)
        faq_frame.pack(fill=tk.X, padx=10, pady=5)

        faq_text = """
Q1: A视频和B视频的时长要求是什么？
A1: 时长可以完全不同。A视频建议至少10秒以避免检测异常，B视频按实际需求选择。
    系统会自动处理时长差异，输出视频以B视频为准。

Q2: 批量处理时如何进行文件配对？
A2: 系统按文件名字母顺序排序后一一对应配对：
    A文件夹第1个文件 + B文件夹第1个文件 = 第1个AB视频
    A文件夹第2个文件 + B文件夹第2个文件 = 第2个AB视频
    以此类推...

Q3: 推荐的参数设置是什么？
A3: 一般使用：嵌入方法=deep_steganography，加密强度=basic，检测规避=adaptive
    高安全需求：嵌入方法=neural_embedding，加密强度=military，检测规避=ai_powered

Q4: 输出视频的质量如何？
A4: 输出视频保持B视频的原始画质和分辨率，只是嵌入了A视频的元数据特征。
    视觉效果与B视频基本一致，不会明显降低画质。

Q5: 如何验证AB视频是否成功？
A5: 系统会生成.mapping.json文件记录处理信息，可通过专业工具验证嵌入效果。"""

        faq_label = ttk.Label(faq_frame, text=faq_text, wraplength=900,
                             font=("Arial", 9), justify=tk.LEFT, foreground="darkslategray")
        faq_label.pack(anchor=tk.W)
        
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请选择文件开始处理")
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                          maximum=100, length=200)
        self.progress_bar.pack(side=tk.RIGHT, padx=10, pady=5)

    # 辅助功能方法
    def open_folder(self, folder_path):
        """打开文件夹"""
        if not folder_path or not os.path.exists(folder_path):
            messagebox.showwarning("警告", "文件夹路径无效或不存在")
            return

        try:
            system = platform.system()
            if system == "Windows":
                os.startfile(folder_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {str(e)}")

    def create_output_directory_frame(self, parent, tab_type, title="输出目录"):
        """创建输出目录选择框架"""
        output_frame = ttk.LabelFrame(parent, text=title, padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=5)

        # 目录显示和操作区域
        dir_frame = ttk.Frame(output_frame)
        dir_frame.pack(fill=tk.X)

        # 目录路径显示
        path_frame = ttk.Frame(dir_frame)
        path_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(path_frame, text="输出路径:").pack(side=tk.LEFT)

        # 创建路径显示标签
        path_var = tk.StringVar()
        path_var.set("未选择输出目录")
        path_label = ttk.Label(path_frame, textvariable=path_var,
                              foreground="gray", relief="sunken",
                              padding=(5, 2))
        path_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

        # 存储路径变量的引用
        setattr(self, f"{tab_type}_output_path_var", path_var)
        setattr(self, f"{tab_type}_output_path_label", path_label)

        # 按钮区域
        btn_frame = ttk.Frame(dir_frame)
        btn_frame.pack(fill=tk.X)

        # 选择目录按钮
        ttk.Button(btn_frame, text="📂 选择输出目录",
                  command=lambda: self.select_output_directory(tab_type)).pack(side=tk.LEFT, padx=5)

        # 打开目录按钮
        ttk.Button(btn_frame, text="📁 打开输出目录",
                  command=lambda: self.open_output_directory(tab_type)).pack(side=tk.LEFT, padx=5)

        # 创建目录按钮
        ttk.Button(btn_frame, text="➕ 新建目录",
                  command=lambda: self.create_new_output_directory(tab_type)).pack(side=tk.LEFT, padx=5)

        # 如果支持拖拽，启用拖拽功能
        if DND_AVAILABLE:
            output_frame.drop_target_register(DND_FILES)
            output_frame.dnd_bind('<<Drop>>', lambda event: self.on_output_folder_dropped(event, tab_type))

            # 添加拖拽提示
            drag_hint = ttk.Label(output_frame, text="💡 提示: 可直接拖拽文件夹到此区域设置输出目录",
                                 font=("Arial", 9), foreground="blue")
            drag_hint.pack(pady=(5, 0))

        return output_frame

    def on_output_folder_dropped(self, event, tab_type):
        """处理输出文件夹拖拽事件"""
        try:
            # 解析拖拽的文件/文件夹
            files = self.root.tk.splitlist(event.data)
            if files:
                folder_path = files[0]
                # 如果是文件，获取其目录
                if os.path.isfile(folder_path):
                    folder_path = os.path.dirname(folder_path)

                if os.path.isdir(folder_path):
                    self.set_output_directory(tab_type, folder_path)
                    self.status_var.set(f"输出目录已设置: {os.path.basename(folder_path)}")
                else:
                    messagebox.showwarning("警告", "请拖拽有效的文件夹")
        except Exception as e:
            messagebox.showerror("错误", f"设置输出目录失败: {str(e)}")

    def select_output_directory(self, tab_type):
        """选择输出目录"""
        folder = filedialog.askdirectory(title=f"选择{tab_type}功能的输出目录")
        if folder:
            self.set_output_directory(tab_type, folder)

    def set_output_directory(self, tab_type, folder_path):
        """设置输出目录"""
        self.output_directories[tab_type] = folder_path
        path_var = getattr(self, f"{tab_type}_output_path_var")
        path_label = getattr(self, f"{tab_type}_output_path_label")

        path_var.set(folder_path)
        path_label.config(foreground="black")
        self.status_var.set(f"{tab_type}输出目录已设置")

    def open_output_directory(self, tab_type):
        """打开输出目录"""
        folder_path = self.output_directories.get(tab_type, '')
        if folder_path:
            self.open_folder(folder_path)
        else:
            messagebox.showinfo("提示", "请先选择输出目录")

    def create_new_output_directory(self, tab_type):
        """创建新的输出目录"""
        base_dir = filedialog.askdirectory(title="选择创建新目录的位置")
        if base_dir:
            # 弹出对话框输入目录名
            from tkinter import simpledialog
            dir_name = simpledialog.askstring("新建目录", "请输入新目录名称:",
                                            initialvalue=f"卡妙_{tab_type}_输出")
            if dir_name:
                new_dir = os.path.join(base_dir, dir_name)
                try:
                    os.makedirs(new_dir, exist_ok=True)
                    self.set_output_directory(tab_type, new_dir)
                    messagebox.showinfo("成功", f"目录创建成功: {new_dir}")
                except Exception as e:
                    messagebox.showerror("错误", f"创建目录失败: {str(e)}")

    # 拖拽事件处理
    def on_files_dropped(self, event, tab_type, file_type):
        """处理文件拖拽事件"""
        try:
            # 解析拖拽的文件/文件夹
            files = self.root.tk.splitlist(event.data)
            if not files:
                return

            # 过滤和收集文件
            valid_files = []

            for item in files:
                if os.path.isfile(item):
                    # 检查文件类型
                    if self.is_valid_file_type(item, file_type):
                        valid_files.append(item)
                elif os.path.isdir(item):
                    # 扫描文件夹
                    folder_files = self.scan_folder_for_files(item, file_type)
                    valid_files.extend(folder_files)

            if valid_files:
                # 添加到文件列表
                if tab_type not in self.current_files:
                    self.current_files[tab_type] = []
                self.current_files[tab_type].extend(valid_files)
                self.update_file_list(tab_type)
                self.status_var.set(f"拖拽添加了 {len(valid_files)} 个文件到 {tab_type} 列表")
            else:
                messagebox.showinfo("提示", f"拖拽的文件中没有找到有效的 {file_type} 文件")

        except Exception as e:
            messagebox.showerror("错误", f"处理拖拽文件失败: {str(e)}")

    def on_a_video_dropped(self, event):
        """处理A视频拖拽事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            if files:
                file_path = files[0]
                if os.path.isfile(file_path) and self.is_valid_file_type(file_path, "video"):
                    self.current_a_video = file_path
                    self.a_video_label.config(text=os.path.basename(file_path), foreground="black")
                    self.status_var.set("A视频已通过拖拽设置")
                elif os.path.isdir(file_path):
                    # 如果拖拽的是文件夹，选择第一个视频文件
                    video_files = self.scan_folder_for_files(file_path, "video")
                    if video_files:
                        self.current_a_video = video_files[0]
                        self.a_video_label.config(text=f"{os.path.basename(video_files[0])} (来自文件夹)", foreground="black")
                        self.status_var.set(f"A视频已设置: 从文件夹中选择了第一个视频文件")
                    else:
                        messagebox.showwarning("警告", "拖拽的文件夹中没有找到视频文件")
                else:
                    messagebox.showwarning("警告", "请拖拽有效的视频文件或包含视频的文件夹")
        except Exception as e:
            messagebox.showerror("错误", f"设置A视频失败: {str(e)}")

    def on_b_video_dropped(self, event):
        """处理B视频拖拽事件"""
        try:
            files = self.root.tk.splitlist(event.data)
            if files:
                file_path = files[0]
                if os.path.isfile(file_path) and self.is_valid_file_type(file_path, "video"):
                    self.current_b_video = file_path
                    self.b_video_label.config(text=os.path.basename(file_path), foreground="black")
                    self.status_var.set("B视频已通过拖拽设置")
                elif os.path.isdir(file_path):
                    # 如果拖拽的是文件夹，选择第一个视频文件
                    video_files = self.scan_folder_for_files(file_path, "video")
                    if video_files:
                        self.current_b_video = video_files[0]
                        self.b_video_label.config(text=f"{os.path.basename(video_files[0])} (来自文件夹)", foreground="black")
                        self.status_var.set(f"B视频已设置: 从文件夹中选择了第一个视频文件")
                    else:
                        messagebox.showwarning("警告", "拖拽的文件夹中没有找到视频文件")
                else:
                    messagebox.showwarning("警告", "请拖拽有效的视频文件或包含视频的文件夹")
        except Exception as e:
            messagebox.showerror("错误", f"设置B视频失败: {str(e)}")

    def is_valid_file_type(self, file_path, file_type):
        """检查文件类型是否有效"""
        ext = os.path.splitext(file_path)[1].lower()

        if file_type == "video":
            return ext in {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp'}
        elif file_type == "image":
            return ext in {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}

        return False

    def scan_folder_for_files(self, folder_path, file_type):
        """扫描文件夹中的指定类型文件"""
        valid_files = []

        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if self.is_valid_file_type(file_path, file_type):
                        valid_files.append(file_path)
        except Exception as e:
            print(f"扫描文件夹失败: {e}")

        return valid_files

    # 时间戳计算功能
    def auto_calculate_timestamp(self):
        """自动计算时间戳参数"""
        try:
            # 获取当前选择的视频文件
            if "timestamp" not in self.current_files or not self.current_files["timestamp"]:
                messagebox.showwarning("警告", "请先选择视频文件")
                return

            # 获取第一个视频的时长作为参考
            video_file = self.current_files["timestamp"][0]

            import cv2
            cap = cv2.VideoCapture(video_file)
            if not cap.isOpened():
                messagebox.showerror("错误", "无法读取视频文件")
                return

            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            source_duration = frame_count / fps
            cap.release()

            # 获取当前设置的参数
            normal_duration = float(self.normal_duration.get())
            display_duration = float(self.display_duration.get())

            # 自动计算实际播放时长
            # 实际时长 = 正常播放时长 + (源视频时长 - 正常播放时长) * 压缩比例
            remaining_duration = source_duration - normal_duration
            if remaining_duration > 0:
                # 计算压缩比例，使总时长接近显示时长
                compression_ratio = (display_duration - normal_duration) / remaining_duration
                if compression_ratio < 0.1:
                    compression_ratio = 0.1  # 最小压缩比例
                elif compression_ratio > 1.0:
                    compression_ratio = 1.0  # 最大压缩比例

                actual_duration = normal_duration + remaining_duration * compression_ratio
            else:
                actual_duration = normal_duration

            # 更新界面
            self.actual_duration.delete(0, tk.END)
            self.actual_duration.insert(0, f"{actual_duration:.1f}")

            # 显示计算结果
            messagebox.showinfo("计算结果",
                f"源视频时长: {source_duration:.1f}秒\n"
                f"正常播放: {normal_duration:.1f}秒\n"
                f"显示时长: {display_duration:.1f}秒\n"
                f"实际播放: {actual_duration:.1f}秒\n"
                f"压缩比例: {compression_ratio:.2f}")

        except Exception as e:
            messagebox.showerror("错误", f"计算失败: {str(e)}")

    # 文件选择功能
    def select_files(self, tab_type, file_type):
        """选择文件"""
        if file_type == "video":
            files = filedialog.askopenfilenames(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"), ("所有文件", "*.*")]
            )
        elif file_type == "image":
            files = filedialog.askopenfilenames(
                title="选择图片文件",
                filetypes=[("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff"), ("所有文件", "*.*")]
            )
        else:
            files = []

        if files:
            if tab_type not in self.current_files:
                self.current_files[tab_type] = []
            self.current_files[tab_type].extend(files)
            self.update_file_list(tab_type)
            self.status_var.set(f"已添加 {len(files)} 个文件到 {tab_type} 列表")

    def select_folder(self, tab_type, file_type):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择文件夹")
        if folder:
            if file_type == "video":
                extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
            elif file_type == "image":
                extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}
            else:
                extensions = set()

            files = []
            for root, dirs, filenames in os.walk(folder):
                for filename in filenames:
                    ext = os.path.splitext(filename)[1].lower()
                    if ext in extensions:
                        files.append(os.path.join(root, filename))

            if files:
                if tab_type not in self.current_files:
                    self.current_files[tab_type] = []
                self.current_files[tab_type].extend(files)
                self.update_file_list(tab_type)
                self.status_var.set(f"从文件夹添加了 {len(files)} 个文件到 {tab_type} 列表")
            else:
                messagebox.showinfo("提示", f"文件夹中未找到 {file_type} 文件")

    def clear_files(self, tab_type):
        """清空文件列表"""
        if tab_type in self.current_files:
            self.current_files[tab_type] = []
        self.update_file_list(tab_type)
        self.status_var.set(f"{tab_type} 列表已清空")

    def update_file_list(self, tab_type):
        """更新文件列表显示"""
        listbox_map = {
            "dedup": self.dedup_listbox,
            "timestamp": self.timestamp_listbox,
            "images": self.images_listbox
        }

        if tab_type in listbox_map:
            listbox = listbox_map[tab_type]
            listbox.delete(0, tk.END)

            if tab_type in self.current_files:
                for file_path in self.current_files[tab_type]:
                    display_name = os.path.basename(file_path)
                    listbox.insert(tk.END, display_name)

    def select_a_video(self):
        """选择A视频"""
        file = filedialog.askopenfilename(
            title="选择A视频文件",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"), ("所有文件", "*.*")]
        )
        if file:
            self.current_a_video = file
            self.a_video_label.config(text=os.path.basename(file), foreground="black")
            self.status_var.set("A视频已选择")

    def select_b_video(self):
        """选择B视频"""
        file = filedialog.askopenfilename(
            title="选择B视频文件",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"), ("所有文件", "*.*")]
        )
        if file:
            self.current_b_video = file
            self.b_video_label.config(text=os.path.basename(file), foreground="black")
            self.status_var.set("B视频已选择")

    # 实际处理功能
    def process_deduplication(self):
        """处理视频去重"""
        # 检查去重处理器是否正确初始化
        if not hasattr(self, 'video_deduplicator') or self.video_deduplicator is None:
            messagebox.showerror("错误", "视频去重模块未正确初始化，请检查相关依赖是否完整安装")
            return
            
        if "dedup" not in self.current_files or not self.current_files["dedup"]:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        files = self.current_files["dedup"]

        # 获取所有效果参数
        filter_effect = self.dedup_filter.get()
        transition_effect = self.dedup_transition.get()
        special_effect = self.dedup_effect.get()
        mirror_setting = self.dedup_mirror.get()
        rotation_angle = float(self.dedup_angle.get())
        dedup_method = self.dedup_method.get()
        audio_method = self.dedup_audio.get()
        intensity = self.dedup_intensity.get()
        intensity_map = {"轻度": "light", "中度": "medium", "重度": "heavy", "极重": "extreme"}
        fps = int(self.dedup_fps.get())

        # 构建效果配置
        effects_config = {
            "filter": filter_effect,
            "transition": transition_effect,
            "effect": special_effect,
            "mirror": mirror_setting,
            "rotation": rotation_angle,
            "dedup_method": dedup_method,
            "audio_method": audio_method,
            "intensity": intensity_map[intensity]
        }

        # 检查输出目录
        output_dir = self.output_directories.get("dedup", "")
        if not output_dir:
            output_dir = filedialog.askdirectory(title="选择输出目录")
            if not output_dir:
                return
            self.set_output_directory("dedup", output_dir)

        def process_thread():
            try:
                self.status_var.set("正在批量处理视频去重...")
                self.progress_var.set(0)

                total_files = len(files)
                success_count = 0

                for i, file_path in enumerate(files):
                    self.status_var.set(f"正在处理: {os.path.basename(file_path)} ({i+1}/{total_files})")

                    # 生成输出文件名
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    output_path = os.path.join(output_dir, f"{base_name}_dedup.mp4")

                    # 调用去重处理
                    success = self.video_deduplicator.process_video_file(
                        file_path, output_path, effects_config, fps
                    )

                    if success:
                        success_count += 1

                    # 更新进度
                    progress = ((i + 1) / total_files) * 100
                    self.progress_var.set(progress)
                    self.root.update()

                self.status_var.set(f"视频去重完成！成功处理 {success_count}/{total_files} 个文件")

                # 完成后提供打开输出目录的选项
                result = messagebox.askyesno("完成",
                    f"视频去重批量处理完成！\n成功: {success_count}/{total_files}\n输出目录: {output_dir}\n\n是否打开输出目录？")
                if result:
                    self.open_folder(output_dir)

            except Exception as e:
                self.status_var.set(f"处理失败: {str(e)}")
                messagebox.showerror("错误", f"处理失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=process_thread, daemon=True).start()

    def process_timestamp(self):
        """处理时间戳控制"""
        if "timestamp" not in self.current_files or not self.current_files["timestamp"]:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        files = self.current_files["timestamp"]

        try:
            normal_duration = float(self.normal_duration.get())
            display_duration = float(self.display_duration.get())
            actual_duration = float(self.actual_duration.get())
            fps = int(self.timestamp_fps.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的时长参数")
            return

        # 检查输出目录
        output_dir = self.output_directories.get("timestamp", "")
        if not output_dir:
            output_dir = filedialog.askdirectory(title="选择输出目录")
            if not output_dir:
                return
            self.set_output_directory("timestamp", output_dir)

        def process_thread():
            try:
                self.status_var.set("正在批量处理时间戳控制...")
                self.progress_var.set(0)

                total_files = len(files)
                success_count = 0

                for i, file_path in enumerate(files):
                    self.status_var.set(f"正在处理: {os.path.basename(file_path)} ({i+1}/{total_files})")

                    # 生成输出文件名
                    base_name = os.path.splitext(os.path.basename(file_path))[0]
                    output_path = os.path.join(output_dir, f"{base_name}_timestamp.mp4")

                    # 调用时间戳控制处理
                    success = self.timestamp_controller.create_hybrid_time_spoofed_video(
                        file_path, output_path, normal_duration, display_duration, actual_duration, fps, self.generate_json.get()
                    )

                    if success:
                        success_count += 1

                    # 更新进度
                    progress = ((i + 1) / total_files) * 100
                    self.progress_var.set(progress)
                    self.root.update()

                self.status_var.set(f"时间戳控制完成！成功处理 {success_count}/{total_files} 个文件")

                # 完成后提供打开输出目录的选项
                result = messagebox.askyesno("完成",
                    f"时间戳控制批量处理完成！\n成功: {success_count}/{total_files}\n输出目录: {output_dir}\n\n是否打开输出目录？")
                if result:
                    self.open_folder(output_dir)

            except Exception as e:
                self.status_var.set(f"处理失败: {str(e)}")
                messagebox.showerror("错误", f"处理失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=process_thread, daemon=True).start()

    def process_images_to_video(self):
        """处理图转视频"""
        # 检查是否已经在处理中
        if not self.image_lock.acquire(blocking=False):
            messagebox.showinfo("提示", "图片转视频功能正在处理中，请等待当前任务完成")
            return
            
        # 检查图片转换器是否正确初始化
        if not hasattr(self, 'image_converter') or self.image_converter is None:
            messagebox.showerror("错误", "图片转换模块未正确初始化，请检查相关依赖是否安装完整")
            self.image_lock.release()
            return
            
        if "images" not in self.current_files or not self.current_files["images"]:
            messagebox.showwarning("警告", "请先选择图片文件或文件夹")
            return

        files = self.current_files["images"]

        try:
            duration_per_image = float(self.image_duration.get())
            fps = int(self.images_fps.get())
            transition_duration = float(self.transition_duration.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的参数")
            return

        transition_type = self.transition_type.get()

        # 解析视频方向
        orientation_map = {
            "竖屏(9:16)": "portrait",
            "横屏(16:9)": "landscape",
            "正方形(1:1)": "square"
        }
        orientation = orientation_map.get(self.video_orientation.get(), "portrait")

        # 检查输出目录
        output_dir = self.output_directories.get("images", "")
        if not output_dir:
            output_dir = filedialog.askdirectory(title="选择输出目录")
            if not output_dir:
                return
            self.set_output_directory("images", output_dir)

        def process_thread():
            try:
                self.status_vars['images'].set("正在批量处理图转视频...")
                self.progress_vars['images'].set(0)

                # 按目录分组图片
                dir_groups = {}
                for file_path in files:
                    dir_path = os.path.dirname(file_path)
                    if dir_path not in dir_groups:
                        dir_groups[dir_path] = []
                    dir_groups[dir_path].append(file_path)

                total_groups = len(dir_groups)
                success_count = 0

                for i, (dir_path, group_files) in enumerate(dir_groups.items()):
                    dir_name = os.path.basename(dir_path) or "root"
                    self.status_var.set(f"正在处理: {dir_name} ({i+1}/{total_groups})")

                    # 生成输出文件名
                    output_path = os.path.join(output_dir, f"{dir_name}_slideshow.mp4")

                    # 调用图转视频处理
                    success = self.image_converter.create_slideshow(
                        image_directory=dir_path,
                        output_path=output_path,
                        duration_per_image=duration_per_image,
                        transition_duration=transition_duration,
                        transition_type=transition_type,
                        fps=fps,
                        orientation=orientation
                    )

                    if success:
                        success_count += 1

                    # 更新进度
                    progress = ((i + 1) / total_groups) * 100
                    self.progress_var.set(progress)
                    self.root.update()

                self.status_var.set(f"图转视频完成！成功处理 {success_count}/{total_groups} 个目录")

                # 完成后提供打开输出目录的选项
                result = messagebox.askyesno("完成",
                    f"图转视频批量处理完成！\n成功: {success_count}/{total_groups}\n输出目录: {output_dir}\n\n是否打开输出目录？")
                if result:
                    self.open_folder(output_dir)

            except Exception as e:
                self.status_var.set(f"处理失败: {str(e)}")
                messagebox.showerror("错误", f"处理失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=process_thread, daemon=True).start()

    def process_single_ab_video(self):
        """处理单个AB视频"""
        if not hasattr(self, 'current_a_video') or not hasattr(self, 'current_b_video'):
            messagebox.showwarning("警告", "请先选择A视频和B视频")
            return

        method = self.ab_method.get()
        fps = int(self.ab_fps.get())

        # 检查输出目录
        output_dir = self.output_directories.get("ab", "")
        if not output_dir:
            output_dir = filedialog.askdirectory(title="选择输出目录")
            if not output_dir:
                return
            self.set_output_directory("ab", output_dir)

        # 生成输出文件名
        a_name = os.path.splitext(os.path.basename(self.current_a_video))[0]
        b_name = os.path.splitext(os.path.basename(self.current_b_video))[0]
        output_path = os.path.join(output_dir, f"AB_{a_name}_{b_name}.mp4")

        def process_thread():
            try:
                self.status_var.set("正在创建AB视频...")
                self.progress_var.set(50)

                # 调用AB视频处理
                success = self.ab_processor.create_metadata_overlay(
                    self.current_a_video,
                    self.current_b_video,
                    output_path,
                    method,
                    fps,
                    self.generate_json.get()
                )

                if success:
                    self.status_var.set("AB视频创建完成！")
                    result = messagebox.askyesno("完成",
                        f"AB视频创建完成！\n输出文件: {output_path}\n\n是否打开输出目录？")
                    if result:
                        self.open_folder(os.path.dirname(output_path))
                else:
                    self.status_var.set("AB视频创建失败")
                    messagebox.showerror("错误", "AB视频创建失败")

            except Exception as e:
                self.status_var.set(f"处理失败: {str(e)}")
                messagebox.showerror("错误", f"处理失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=process_thread, daemon=True).start()

    def process_batch_ab_video(self):
        """批量AB视频处理"""
        if "ab_a" not in self.current_files or "ab_b" not in self.current_files:
            messagebox.showwarning("警告", "请先选择A视频文件夹和B视频文件夹")
            return

        a_files = self.current_files.get("ab_a", [])
        b_files = self.current_files.get("ab_b", [])

        if not a_files or not b_files:
            messagebox.showwarning("警告", "A视频或B视频文件夹为空")
            return

        method = self.ab_method.get()

        output_dir = filedialog.askdirectory(title="选择输出目录")
        if not output_dir:
            return

        def process_thread():
            try:
                self.status_var.set("正在批量处理AB视频...")
                self.progress_var.set(0)

                # 配对处理
                min_count = min(len(a_files), len(b_files))
                success_count = 0

                for i in range(min_count):
                    a_file = a_files[i]
                    b_file = b_files[i]

                    self.status_var.set(f"正在处理: AB_{i+1:03d} ({i+1}/{min_count})")

                    # 生成输出文件名
                    output_path = os.path.join(output_dir, f"AB_{i+1:03d}.mp4")

                    # 调用AB视频处理
                    success = self.ab_processor.create_metadata_overlay(
                        a_file, b_file, output_path, method, 30, self.generate_json.get()
                    )

                    if success:
                        success_count += 1

                    # 更新进度
                    progress = ((i + 1) / min_count) * 100
                    self.progress_var.set(progress)
                    self.root.update()

                self.status_var.set(f"批量AB视频处理完成！成功处理 {success_count}/{min_count} 对视频")
                messagebox.showinfo("完成", f"批量AB视频处理完成！\n成功: {success_count}/{min_count}\n输出目录: {output_dir}")

            except Exception as e:
                self.status_var.set(f"处理失败: {str(e)}")
                messagebox.showerror("错误", f"处理失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        threading.Thread(target=process_thread, daemon=True).start()

def main():
    """主函数"""
    try:
        app = EnhancedVideoEditor(None)
        app.root.mainloop()
    except Exception as e:
        print(f"启动失败: {e}")
        messagebox.showerror("启动失败", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
