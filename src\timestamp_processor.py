import subprocess
from pathlib import Path
from typing import Optional, List, Union
import json
import tempfile

class TimestampProcessor:
    def __init__(self):
        self.current_process = None
        self._setup_startupinfo()

    def _setup_startupinfo(self):
        """设置进程启动信息以隐藏命令行窗口"""
        self.startupinfo = subprocess.STARTUPINFO()
        self.startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        self.startupinfo.wShowWindow = subprocess.SW_HIDE

    def _run_ffmpeg_silent(self, cmd: List[str]) -> tuple[bool, str]:
        """静默运行 FFmpeg 命令"""
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self.startupinfo,
                text=True
            )
            self.current_process = process
            stdout, stderr = process.communicate()
            success = process.returncode == 0
            return success, stderr if not success else stdout
        except Exception as e:
            return False, str(e)
        finally:
            self.current_process = None

    def get_video_duration(self, video_path: Union[str, Path]) -> Optional[float]:
        """获取视频时长"""
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'json',
            str(video_path)
        ]
        
        success, output = self._run_ffmpeg_silent(cmd)
        if success:
            try:
                data = json.loads(output)
                return float(data['format']['duration'])
            except Exception:
                pass
        return None

    def add_timestamp(self, video_path: Union[str, Path], output_path: Union[str, Path],
                     start_time: float = 0, interval: float = 1.0,
                     font_size: int = 24, font_color: str = 'white') -> tuple[bool, str]:
        """添加时间戳水印"""
        # 创建临时文件用于过滤器复杂命令
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp:
            filter_complex = (
                f"drawtext=fontsize={font_size}:fontcolor={font_color}:"
                f"x=10:y=10:text='%{{pts\\:hms}}':"
                f"rate={1/interval}:start_number={start_time}"
            )
            temp.write(filter_complex)
            temp.flush()
            
            cmd = [
                'ffmpeg', '-y',
                '-i', str(video_path),
                '-vf', f"@{temp.name}",
                '-c:a', 'copy',
                str(output_path)
            ]
            
            return self._run_ffmpeg_silent(cmd)

    def stop_current_process(self):
        """停止当前进程"""
        if self.current_process:
            try:
                self.current_process.terminate()
            except Exception:
                pass

# 创建全局时间戳处理器实例
timestamp_processor = TimestampProcessor()
