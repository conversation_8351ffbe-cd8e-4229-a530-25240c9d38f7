@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    卡妙视频编辑软件 - 一键打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查项目文件...
if not exist "main.py" (
    echo ❌ 错误: 未找到main.py文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ 错误: 未找到requirements.txt文件
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过
echo.

echo 正在安装/更新依赖包...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成
echo.

echo 开始打包程序...
python build_exe.py
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo           打包完成！
echo ========================================
echo.
echo 📁 输出目录: dist\卡妙视频编辑软件\
echo 🚀 可执行文件: 卡妙视频编辑软件.exe
echo.
echo 💡 提示: 
echo    1. 整个文件夹可以复制到其他电脑运行
echo    2. 首次运行可能需要几秒钟加载时间
echo    3. 建议在SSD硬盘上运行以获得更好性能
echo.

echo 是否现在打开输出目录？(Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    explorer "dist\卡妙视频编辑软件"
)

pause
