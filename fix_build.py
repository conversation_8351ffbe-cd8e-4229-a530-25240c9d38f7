#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版打包脚本 - 解决EXE闪退问题
不修改任何主程序文件，只修复打包配置
"""

import os
import sys
import subprocess
from pathlib import Path

def create_fixed_spec():
    """创建修复版的规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(SPECPATH)

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[
        # 包含FFmpeg可执行文件
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffmpeg.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffprobe.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffplay.exe'), 'ffmpeg_bundle/bin'),
    ],
    datas=[
        # 包含FFmpeg预设和文档
        (str(project_root / 'ffmpeg_bundle' / 'presets'), 'ffmpeg_bundle/presets'),
        (str(project_root / 'ffmpeg_bundle' / 'doc'), 'ffmpeg_bundle/doc'),
        (str(project_root / 'ffmpeg_bundle' / 'LICENSE'), 'ffmpeg_bundle'),
        (str(project_root / 'ffmpeg_bundle' / 'README.txt'), 'ffmpeg_bundle'),
        # 包含源代码模块
        (str(project_root / 'src'), 'src'),
    ],
    hiddenimports=[
        # 核心模块
        'src.core.video_deduplication',
        'src.core.timestamp_control', 
        'src.core.image_to_video',
        'src.core.ab_video',
        'src.gui.enhanced_window',
        'src.gui.drag_drop_frame',
        'src.ffmpeg_setup',
        'src.timestamp_processor',
        'src.video_processor',
        # 第三方库
        'cv2',
        'numpy',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'matplotlib',
        'matplotlib.pyplot',
        'tqdm',
        'imageio',
        'imageio_ffmpeg',
        'tkinterdnd2',
        'ttkbootstrap',
        # 修复scipy相关问题
        'scipy',
        'scipy.ndimage',
        'scipy._lib',
        'scipy._lib._array_api',
        'scipy._lib.array_api_compat',
        'scipy._lib.array_api_compat.numpy',
        'scipy.ndimage._support_alternative_backends',
        # 修复unittest相关问题
        'unittest',
        'unittest.mock',
        'unittest.case',
        'unittest.suite',
        'unittest.loader',
        'unittest.runner',
        'unittest.result',
        'unittest.signals',
        'unittest.util',
        # 修复numpy.testing问题
        'numpy.testing',
        'numpy.testing._private',
        'numpy.testing._private.utils',
        # 其他必要模块
        'tempfile',
        'threading',
        'subprocess',
        'json',
        'hashlib',
        'pathlib',
        'glob',
        'random',
        'math',
        'typing',
        'collections',
        'collections.abc',
        'importlib',
        'importlib.util',
        'importlib.machinery',
        'platform',
        'warnings',
        'weakref',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 只排除真正不需要的模块
        'test',
        'tests',
        'doctest',
        'pdb',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VideoEditor_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 保留控制台以显示调试信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VideoEditor_Fixed',
)
'''
    
    with open('video_editor_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 修复版规格文件 video_editor_fixed.spec 创建成功")

def build_fixed_executable():
    """构建修复版可执行文件"""
    print("🔨 开始构建修复版可执行文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用修复版规格文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "video_editor_fixed.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 修复版构建成功!")
            return True
        else:
            print("❌ 修复版构建失败!")
            print("\n错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {str(e)}")
        return False

def create_fixed_readme():
    """创建修复版使用说明"""
    readme_content = """# 卡妙视频编辑软件 - 修复版

## 🔧 修复说明
此版本修复了原版本的闪退问题，主要解决了：
- scipy模块依赖问题
- unittest模块缺失问题
- numpy.testing模块问题

## 🚀 使用方法
1. 双击运行 "卡妙视频编辑软件_修复版.exe"
2. 首次运行可能需要几秒钟加载时间
3. 如果仍有问题，请以管理员身份运行

## 💡 功能说明
- **视频去重**: 通过特效绕过平台原创检测
- **时间戳控制**: 分段播放速度控制
- **图转视频**: 图片序列转视频，支持多种转场效果
- **AB视频**: 平台检测A视频，用户观看B视频
- **批量处理**: 支持所有功能的批量操作

## 📋 系统要求
- Windows 7/8/10/11 (64位)
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 🔧 故障排除
1. 如果程序无法启动，请检查杀毒软件设置
2. 如果提示缺少文件，请确保所有文件都在同一目录
3. 建议在SSD硬盘上运行以获得更好性能

---
修复版本 - 解决闪退问题
"""
    
    output_dir = Path("dist/VideoEditor_Fixed")
    if output_dir.exists():
        with open(output_dir / 'README_修复版.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 修复版使用说明文件创建成功")

def main():
    """主函数"""
    print("🔧 卡妙视频编辑软件 - 修复版打包工具")
    print("=" * 50)
    print("此工具专门修复EXE闪退问题，不会修改任何主程序文件")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller 安装成功")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return
    
    # 创建修复版规格文件
    create_fixed_spec()
    
    # 构建修复版可执行文件
    if build_fixed_executable():
        # 创建说明文件
        create_fixed_readme()
        
        print("\n" + "=" * 50)
        print("🎉 修复版打包完成!")
        print(f"📁 输出目录: {os.path.abspath('dist/VideoEditor_Fixed')}")
        print("📋 包含文件:")
        print("   - VideoEditor_Fixed.exe (修复版主程序)")
        print("   - ffmpeg_bundle/ (视频处理工具)")
        print("   - src/ (源代码模块)")
        print("   - 其他依赖文件")
        print("   - README_修复版.txt (使用说明)")
        print("\n💡 提示: 此版本已修复闪退问题，可以正常运行")
        print("🚀 现在可以测试运行修复版程序了！")
    else:
        print("❌ 修复版打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
