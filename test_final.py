#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复版程序
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_final_executable():
    """测试最终版可执行文件"""
    exe_path = Path("dist/VideoEditor_Final/VideoEditor_Final.exe")
    
    if not exe_path.exists():
        print("❌ Final version executable not found")
        print("Please run: python final_fix.py")
        return False
    
    print("✅ Final version executable exists")
    print(f"📁 File path: {exe_path.absolute()}")
    print(f"📊 File size: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    return True

def test_final_startup():
    """测试最终版程序启动"""
    exe_path = Path("dist/VideoEditor_Final/VideoEditor_Final.exe")
    
    print("🚀 Testing final version startup...")
    print("Note: Program will auto-close after 15 seconds")
    
    try:
        # 启动程序
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # 等待15秒
        time.sleep(15)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Final version startup SUCCESS!")
            print("✅ Program is running normally, no crash!")
            print("✅ All dependency issues have been resolved!")
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            # 进程已经结束，可能有错误
            stdout, stderr = process.communicate()
            print("❌ Final version still crashes")
            if stderr:
                print(f"Error: {stderr.decode('utf-8', errors='ignore')}")
            if stdout:
                print(f"Output: {stdout.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ Startup test failed: {e}")
        return False

def show_all_versions():
    """显示所有版本对比"""
    print("\n📊 All Versions Comparison:")
    
    versions = [
        ("Original", "dist/卡妙视频编辑软件/卡妙视频编辑软件.exe"),
        ("Fixed", "dist/VideoEditor_Fixed/VideoEditor_Fixed.exe"),
        ("Final", "dist/VideoEditor_Final/VideoEditor_Final.exe")
    ]
    
    for name, path in versions:
        exe_path = Path(path)
        if exe_path.exists():
            size = exe_path.stat().st_size / 1024 / 1024
            print(f"{name:8}: {size:6.1f} MB - {path}")
        else:
            print(f"{name:8}: Not found - {path}")

def main():
    """主函数"""
    print("🧪 Final Version Test Tool")
    print("=" * 30)
    
    if not os.path.exists("dist/VideoEditor_Final"):
        print("❌ Final version not found")
        print("Please run: python final_fix.py")
        print("Or double-click: final_fix.bat")
        return
    
    print("Testing final version...")
    print()
    
    # 测试文件存在性
    if not test_final_executable():
        return
    
    # 测试启动
    startup_success = test_final_startup()
    
    # 显示所有版本
    show_all_versions()
    
    print("\n" + "=" * 30)
    if startup_success:
        print("🎉 FINAL VERSION TEST PASSED!")
        print("✅ Crash issue completely resolved!")
        print("🚀 Ready for deployment to other computers!")
        print("\n💡 Deployment instructions:")
        print("1. Copy entire 'VideoEditor_Final' folder to target computer")
        print("2. Double-click 'VideoEditor_Final.exe' to run")
        print("3. No Python installation required on target computer")
    else:
        print("❌ Final version still has issues")
        print("Please check error messages above")

if __name__ == "__main__":
    main()
