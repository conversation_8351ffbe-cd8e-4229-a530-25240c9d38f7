@echo off
echo.
echo ========================================
echo   Video Editor - Ultimate Fix
echo ========================================
echo.
echo This version places FFmpeg in multiple locations
echo to ensure it's ALWAYS found!
echo.

python ultimate_fix.py

if errorlevel 1 (
    echo.
    echo Ultimate build failed
    echo Please check error messages
    pause
    exit /b 1
)

echo.
echo ========================================
echo      Ultimate Version Complete!
echo ========================================
echo.
echo Output: dist\VideoEditor_Ultimate\
echo Executable: VideoEditor_Ultimate.exe
echo.
echo FFmpeg locations:
echo    - Same directory as EXE (direct access)
echo    - bin\ subdirectory  
echo    - _internal\ffmpeg_bundle\bin\
echo.
echo FFmpeg path issues should be COMPLETELY resolved!
echo.

echo Open output directory now? (Y/N)
set /p choice=Choose: 
if /i "%choice%"=="Y" (
    explorer "dist\VideoEditor_Ultimate"
)

pause
