#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡妙视频编辑软件主程序
包含四个核心功能：
1. 视频去重 - 通过特效绕过平台原创检测
2. 时间戳控制 - 分段播放速度控制，前段正常播放，后段快速播完
3. 图转视频 - 图片序列转视频，支持多种转场效果
4. AB视频 - 平台检测A视频，用户观看B视频
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys

# 尝试导入拖拽功能
try:
    from tkinterdnd2 import TkinterDnD
    DND_AVAILABLE = True
except ImportError:
    DND_AVAILABLE = False

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.gui.enhanced_window import EnhancedVideoEditor

def check_dependencies():
    """检查依赖项"""
    try:
        import cv2
        import numpy as np
        import PIL
        from tqdm import tqdm
        print("✓ 所有依赖项检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖项: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    """主函数"""
    print("🎬 启动卡妙视频编辑软件...")
    print("=" * 50)

    # 检查依赖项
    if not check_dependencies():
        input("按回车键退出...")
        return

    try:
        # 创建增强版界面 - 让界面类自己处理拖拽初始化
        app = EnhancedVideoEditor(None)

        print("✓ 增强版界面已启动")
        print("=" * 50)
        print("功能说明：")
        print("1. 视频去重 - 添加特效绕过原创检测")
        print("2. 时间戳控制 - 前段正常播放，后段快速播完")
        print("3. 图转视频 - 图片序列转换为视频")
        print("4. AB视频 - 平台检测与用户观看分离")
        print("5. 批量处理 - 支持所有功能的实际批量操作")
        print("6. 直观操作 - 清晰的操作步骤和进度显示")
        print("=" * 50)

        # 启动主循环
        app.root.mainloop()

    except Exception as e:
        print(f"✗ 启动失败: {str(e)}")
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
