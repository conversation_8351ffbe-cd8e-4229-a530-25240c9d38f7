#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳控制模块 - 实现真正的进度条快速跳跃技术
基于参考代码重构，支持线性、爆发式和阶梯式跳跃模式
"""

import cv2
import numpy as np
import os
import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Callable
from tqdm import tqdm

class TimestampManipulationProcessor:
    """时间戳操控处理器 - 基于参考代码"""

    def __init__(self):
        self.ffmpeg_path = "ffmpeg"
        self.ffprobe_path = "ffprobe"

    def check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            subprocess.run([self.ffmpeg_path, "-version"],
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    def get_video_info(self, video_path: str) -> Optional[Dict]:
        """获取视频信息"""
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)

            # 提取视频流信息
            video_stream = None
            audio_stream = None

            for stream in info.get("streams", []):
                if stream.get("codec_type") == "video" and video_stream is None:
                    video_stream = stream
                elif stream.get("codec_type") == "audio" and audio_stream is None:
                    audio_stream = stream

            if not video_stream:
                return None

            duration = float(info.get("format", {}).get("duration", 0))
            fps = eval(video_stream.get("r_frame_rate", "30/1"))
            width = int(video_stream.get("width", 0))
            height = int(video_stream.get("height", 0))

            return {
                "duration": duration,
                "fps": fps,
                "width": width,
                "height": height,
                "video_stream": video_stream,
                "audio_stream": audio_stream,
                "format": info.get("format", {})
            }

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return None

    def create_timestamp_manipulation_filter(self,
                                            original_duration: float,
                                            target_display_duration: float,
                                            actual_play_duration: float,
                                            target_fps: float = 30.0) -> str:
        """
        创建时间戳操控滤镜

        Args:
            original_duration: 原视频时长
            target_display_duration: 目标显示时长（进度条总长度）
            actual_play_duration: 实际播放时长
        """

        # 计算时间戳压缩比例
        # 我们需要让视频在actual_play_duration时间内播放完，但时间戳跨度为target_display_duration
        timestamp_scale = target_display_duration / actual_play_duration
        content_speed = original_duration / actual_play_duration

        # 构建复杂滤镜链
        filter_complex = []

        # 视频处理：压缩内容到实际播放时长，然后调整时间戳，并设置目标帧率
        video_filter = f"[0:v]fps={target_fps},setpts=PTS/{content_speed},setpts=PTS*{timestamp_scale}[v_out]"
        filter_complex.append(video_filter)

        # 音频处理：调整音频速度和时间戳
        if content_speed <= 2.0:
            audio_filter = f"[0:a]atempo={content_speed},asetpts=PTS*{timestamp_scale}[a_out]"
        else:
            # 使用多级atempo处理高倍速
            atempo_stages = []
            remaining_speed = content_speed
            while remaining_speed > 2.0:
                atempo_stages.append("atempo=2.0")
                remaining_speed /= 2.0
            if remaining_speed > 0.5:
                atempo_stages.append(f"atempo={remaining_speed}")

            audio_filter = f"[0:a]{','.join(atempo_stages)},asetpts=PTS*{timestamp_scale}[a_out]"

        filter_complex.append(audio_filter)

        return ";".join(filter_complex)

    def process_timestamp_manipulation(self,
                                     input_path: str,
                                     output_path: str,
                                     target_display_duration: float = 5.0,
                                     actual_play_duration: float = 1.0,
                                     target_fps: float = 30.0,
                                     progress_callback: Optional[Callable] = None) -> bool:
        """
        处理时间戳操控

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            target_display_duration: 目标显示时长（进度条显示的总时长）
            actual_play_duration: 实际播放时长
            progress_callback: 进度回调函数
        """

        try:
            # 获取视频信息
            video_info = self.get_video_info(input_path)
            if not video_info:
                return False

            original_duration = video_info["duration"]
            fps = video_info["fps"]

            if progress_callback:
                progress_callback(f"原视频: {original_duration:.1f}s, {fps:.1f}fps")
                progress_callback(f"目标: 显示{target_display_duration:.1f}s，实际播放{actual_play_duration:.1f}s，输出{target_fps:.1f}fps")

            # 创建时间戳操控滤镜
            filter_complex = self.create_timestamp_manipulation_filter(
                original_duration, target_display_duration, actual_play_duration, target_fps
            )

            if progress_callback:
                progress_callback("构建时间戳操控滤镜...")

            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-filter_complex", filter_complex,
                "-map", "[v_out]",
                "-map", "[a_out]",
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                "-profile:v", "baseline",
                "-level", "3.0",
                "-pix_fmt", "yuv420p",
                "-r", str(target_fps),  # 明确指定输出帧率
                "-c:a", "aac",
                "-b:a", "128k",
                "-ar", "44100",
                "-movflags", "+faststart",
                "-y",
                output_path
            ]

            if progress_callback:
                progress_callback("应用时间戳操控效果...")

            # 执行FFmpeg命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                universal_newlines=True
            )

            # 监控进度
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output and progress_callback:
                    # 解析FFmpeg输出中的进度信息
                    if "time=" in output:
                        try:
                            time_str = output.split("time=")[1].split()[0]
                            # 简单的进度显示
                            progress_callback(f"处理中: {time_str}")
                        except:
                            pass

            # 等待进程完成
            return_code = process.wait()

            if return_code == 0:
                if progress_callback:
                    progress_callback("时间戳操控完成")
                return True
            else:
                if progress_callback:
                    progress_callback(f"处理失败，返回码: {return_code}")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"处理异常: {e}")
            return False

    def create_advanced_timestamp_filter(self,
                                       original_duration: float,
                                       target_display_duration: float,
                                       actual_play_duration: float,
                                       target_fps: float = 30.0,
                                       jump_mode: str = "linear") -> str:
        """
        创建高级时间戳滤镜

        Args:
            jump_mode: 跳跃模式
                - "linear": 线性快速跳跃
                - "burst": 爆发式跳跃
                - "stepped": 阶梯式跳跃
        """

        content_speed = original_duration / actual_play_duration

        if jump_mode == "linear":
            # 线性快速跳跃：进度条匀速快进
            timestamp_scale = target_display_duration / actual_play_duration
            video_filter = f"[0:v]fps={target_fps},setpts=PTS/{content_speed},setpts=PTS*{timestamp_scale}[v_out]"

            if content_speed <= 2.0:
                audio_filter = f"[0:a]atempo={content_speed},asetpts=PTS*{timestamp_scale}[a_out]"
            else:
                # 使用多级atempo处理高倍速
                atempo_stages = []
                remaining_speed = content_speed
                while remaining_speed > 2.0:
                    atempo_stages.append("atempo=2.0")
                    remaining_speed /= 2.0
                if remaining_speed > 0.5:
                    atempo_stages.append(f"atempo={remaining_speed}")

                audio_filter = f"[0:a]{','.join(atempo_stages)},asetpts=PTS*{timestamp_scale}[a_out]"

        elif jump_mode == "burst":
            # 爆发式跳跃：快速播放后时间戳跳跃
            video_filter = f"[0:v]fps={target_fps},setpts=PTS/{content_speed}[v_fast];[v_fast]setpts='if(gte(T,{actual_play_duration}),PTS+{target_display_duration-actual_play_duration}/TB,PTS)'[v_out]"

            if content_speed <= 2.0:
                audio_filter = f"[0:a]atempo={content_speed}[a_fast];[a_fast]asetpts='if(gte(T,{actual_play_duration}),PTS+{target_display_duration-actual_play_duration}/TB,PTS)'[a_out]"
            else:
                atempo_stages = []
                remaining_speed = content_speed
                while remaining_speed > 2.0:
                    atempo_stages.append("atempo=2.0")
                    remaining_speed /= 2.0
                if remaining_speed > 0.5:
                    atempo_stages.append(f"atempo={remaining_speed}")

                audio_filter = f"[0:a]{','.join(atempo_stages)}[a_fast];[a_fast]asetpts='if(gte(T,{actual_play_duration}),PTS+{target_display_duration-actual_play_duration}/TB,PTS)'[a_out]"

        elif jump_mode == "stepped":
            # 阶梯式跳跃：分段跳跃
            step_count = 5
            step_duration = actual_play_duration / step_count
            step_jump = (target_display_duration - actual_play_duration) / step_count

            video_filter = f"[0:v]fps={target_fps},setpts=PTS/{content_speed}[v_fast];"
            audio_filter = f"[0:a]atempo={content_speed}[a_fast];" if content_speed <= 2.0 else f"[0:a]atempo=2.0,atempo={content_speed/2.0}[a_fast];"

            # 添加阶梯跳跃逻辑
            for i in range(step_count):
                step_time = step_duration * (i + 1)
                jump_amount = step_jump * (i + 1)
                video_filter += f"[v_fast]setpts='if(gte(T,{step_time}),PTS+{jump_amount}/TB,PTS)'[v_step{i}];"
                audio_filter += f"[a_fast]asetpts='if(gte(T,{step_time}),PTS+{jump_amount}/TB,PTS)'[a_step{i}];"

            video_filter = video_filter.replace(f"[v_step{step_count-1}]", "[v_out]")
            audio_filter = audio_filter.replace(f"[a_step{step_count-1}]", "[a_out]")

        else:
            # 默认线性模式
            timestamp_scale = target_display_duration / actual_play_duration
            video_filter = f"[0:v]fps={target_fps},setpts=PTS/{content_speed},setpts=PTS*{timestamp_scale}[v_out]"
            audio_filter = f"[0:a]atempo={content_speed},asetpts=PTS*{timestamp_scale}[a_out]"

        return f"{video_filter};{audio_filter}"

    def batch_process_timestamp_manipulation(self,
                                           input_files: List[str],
                                           output_dir: str,
                                           target_display_duration: float = 5.0,
                                           actual_play_duration: float = 1.0,
                                           target_fps: float = 30.0,
                                           jump_mode: str = "linear",
                                           progress_callback: Optional[Callable] = None) -> Dict[str, bool]:
        """批量处理时间戳操控"""

        results = {}

        for i, input_file in enumerate(input_files):
            if progress_callback:
                progress_callback(f"处理文件 {i+1}/{len(input_files)}: {Path(input_file).name}")

            # 生成输出文件名
            input_path = Path(input_file)
            output_filename = f"{input_path.stem}_时间戳操控_{actual_play_duration:.1f}s播完显示{target_display_duration:.1f}s{input_path.suffix}"
            output_path = Path(output_dir) / output_filename

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 处理单个文件
            success = self.process_timestamp_manipulation(
                str(input_path),
                str(output_path),
                target_display_duration,
                actual_play_duration,
                target_fps,
                progress_callback
            )

            results[str(input_path)] = success

            if progress_callback:
                status = "✅ 成功" if success else "❌ 失败"
                progress_callback(f"{status}: {output_filename}")

        return results


class TimestampController:
    """兼容性包装器 - 保持与现有GUI的兼容性"""

    def __init__(self):
        """初始化时间戳控制器"""
        self.temp_dir = tempfile.mkdtemp()
        self.metadata_methods = ["header", "footer", "invisible", "frame_duplication"]
        self.processor = TimestampManipulationProcessor()

    def create_hybrid_time_spoofed_video(self, input_path: str, output_path: str,
                                        normal_play_duration: float,
                                        display_duration: float = 0.0,
                                        actual_duration: float = 0.0,
                                        fps: int = 30, generate_json: bool = False) -> bool:
        """
        创建混合时间戳欺骗视频 - 使用新的处理器

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            normal_play_duration: 正常播放时长（未使用，保持兼容性）
            display_duration: 播放器显示时长
            actual_duration: 实际播放时长
            fps: 帧率（未使用，保持兼容性）
        """
        try:
            print(f"🎬 使用参考代码处理时间戳操控")
            print(f"   输入: {input_path}")
            print(f"   输出: {output_path}")
            print(f"   显示时长: {display_duration}秒")
            print(f"   实际播放时长: {actual_duration}秒")
            print(f"   输出帧率: {fps}fps")

            # 使用新的处理器
            success = self.processor.process_timestamp_manipulation(
                input_path=input_path,
                output_path=output_path,
                target_display_duration=display_duration,
                actual_play_duration=actual_duration,
                target_fps=float(fps),
                progress_callback=lambda msg: print(f"  📋 {msg}")
            )

            if success:
                print(f"✅ 时间戳操控成功完成")
            else:
                print(f"❌ 时间戳操控失败")

            return success

        except Exception as e:
            print(f"❌ 时间戳操控异常: {e}")
            return False

    def process_timestamp_manipulation(self, *args, **kwargs):
        """兼容性方法 - 委托给处理器"""
        return self.processor.process_timestamp_manipulation(*args, **kwargs)

    def batch_process_timestamp_manipulation(self, *args, **kwargs):
        """兼容性方法 - 委托给处理器"""
        return self.processor.batch_process_timestamp_manipulation(*args, **kwargs)


# 测试函数
def test_timestamp_manipulation():
    """测试时间戳操控功能"""
    processor = TimestampManipulationProcessor()

    if not processor.check_ffmpeg():
        print("❌ FFmpeg不可用")
        return False

    print("✅ FFmpeg可用")
    print("🎯 时间戳操控处理器测试完成")
    return True


class TimestampController:
    """兼容性包装器 - 保持与现有GUI的兼容性"""

    def __init__(self):
        """初始化时间戳控制器"""
        self.temp_dir = tempfile.mkdtemp()
        self.metadata_methods = ["header", "footer", "invisible", "frame_duplication"]
        self.processor = TimestampManipulationProcessor()

    def create_hybrid_time_spoofed_video(self, input_path: str, output_path: str,
                                        normal_play_duration: float,
                                        display_duration: float = 0.0,
                                        actual_duration: float = 0.0,
                                        fps: int = 30, generate_json: bool = False) -> bool:
        """
        创建混合时间戳欺骗视频 - 使用新的处理器

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            normal_play_duration: 正常播放时长（保持兼容性）
            display_duration: 播放器显示时长
            actual_duration: 实际播放时长
            fps: 帧率
            generate_json: 是否生成JSON信息文件
        """
        try:
            # 使用新的处理器
            result = self.processor.process_timestamp_manipulation(
                input_path=input_path,
                output_path=output_path,
                target_display_duration=display_duration,
                actual_play_duration=actual_duration,
                target_fps=float(fps),
                progress_callback=lambda msg: print(f"🎬 {msg}")
            )

            # 如果处理成功且需要生成JSON文件
            if result and generate_json:
                try:
                    self.save_spoofing_info(input_path, output_path,
                                          actual_duration/display_duration if display_duration > 0 else 1.0,
                                          {"fps": fps, "display_duration": display_duration, "actual_duration": actual_duration},
                                          generate_json=True)
                except Exception as json_error:
                    print(f"⚠️ JSON文件生成失败: {json_error}")

            return result

        except Exception as e:
            print(f"❌ 时间戳操控失败: {e}")
            return False

    def create_timestamp_spoofed_video(self, input_path: str, output_path: str,
                                     content_duration: float, display_duration: float,
                                     actual_duration: float, fps: int, original_info: Dict,
                                     generate_json: bool = False) -> bool:
        """
        创建真正的时间戳欺骗视频

        核心逻辑：
        1. 提取源视频前N秒的内容
        2. 将这N秒内容制作成实际播放时长的视频
        3. 修改元数据让播放器认为视频长度是display_duration
        4. 实现：实际播放actual_duration秒，但进度条走display_duration秒

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            content_duration: 观看内容时长（从源视频开头提取多少秒）
            display_duration: 播放器显示的总时长（进度条显示时长）
            actual_duration: 实际播放时长（真实播放时间）
            fps: 输出帧率
            original_info: 原始视频信息
            generate_json: 是否生成JSON信息文件

        Returns:
            是否成功
        """
        try:
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                print(f"无法打开视频文件: {input_path}")
                return False

            # 获取原始视频属性
            original_fps = original_info['fps']
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 第一步：提取前N秒的内容帧
            content_frame_count = int(content_duration * original_fps)
            print(f"提取内容: 前{content_duration}秒 ({content_frame_count}帧)")

            # 提取内容帧
            content_frames = []
            for i in range(content_frame_count):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                if ret:
                    content_frames.append(frame)
                else:
                    break

            cap.release()

            if not content_frames:
                print("无法提取视频内容")
                return False

            # 第二步：创建实际播放时长的视频
            # 使用正常帧率创建视频，后续通过ffmpeg修改时间戳

            print(f"实际播放时长: {actual_duration}秒")
            print(f"播放器显示时长: {display_duration}秒")
            print(f"输出帧率: {fps} FPS")

            # 创建视频编码器，使用正常帧率
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                print(f"无法创建输出视频文件: {output_path}")
                return False

            # 第三步：将内容帧写入，时长为actual_duration
            total_output_frames = int(actual_duration * fps)

            for i in range(total_output_frames):
                # 在内容帧中循环或重复播放
                frame_index = int(i * len(content_frames) / total_output_frames)
                if frame_index >= len(content_frames):
                    frame_index = len(content_frames) - 1

                out.write(content_frames[frame_index])

            out.release()

            # 第四步：使用ffmpeg修改时间戳，实现真正的时间轴欺骗
            return self.apply_timestamp_spoofing(output_path, content_duration, actual_duration, display_duration)

        except Exception as e:
            print(f"创建时间戳欺骗视频失败: {str(e)}")
            return False

    def apply_timestamp_spoofing(self, video_path: str, normal_play_duration: float, actual_duration: float, display_duration: float) -> bool:
        """
        应用真正的时间戳欺骗

        基于参考项目的增强版时间戳操控技术：
        1. 使用复杂的FFmpeg滤镜链
        2. 双重时间戳调整：内容速度 + 时间戳显示
        3. 音频同步处理
        4. 确保30帧率输出

        Args:
            video_path: 视频文件路径
            actual_duration: 实际播放时长
            display_duration: 播放器显示时长

        Returns:
            是否成功
        """
        try:
            import subprocess
            import os
            import json

            temp_path = video_path + ".temp.mp4"

            print(f"应用增强版时间戳操控:")
            print(f"- 实际播放时长: {actual_duration}秒")
            print(f"- 目标显示时长: {display_duration}秒")

            # 获取原视频信息
            video_info = self._get_video_info_detailed(video_path)
            if not video_info:
                print("❌ 无法获取视频信息")
                return False

            original_duration = video_info["duration"]
            original_fps = video_info["fps"]

            print(f"- 原视频时长: {original_duration:.2f}秒")
            print(f"- 原视频帧率: {original_fps:.2f}fps")

            # 使用增强版时间戳操控
            return self._apply_enhanced_timestamp_manipulation(
                video_path, temp_path, normal_play_duration,
                actual_duration, display_duration, 30.0, video_info
            )

        except Exception as e:
            print(f"应用时间戳欺骗失败: {str(e)}")
            return False

    def _get_video_info_detailed(self, video_path: str) -> dict:
        """获取详细的视频信息"""
        try:
            import subprocess
            import json

            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)

            # 提取视频流信息
            video_stream = None
            audio_stream = None

            for stream in info.get("streams", []):
                if stream.get("codec_type") == "video" and video_stream is None:
                    video_stream = stream
                elif stream.get("codec_type") == "audio" and audio_stream is None:
                    audio_stream = stream

            if not video_stream:
                return None

            duration = float(info.get("format", {}).get("duration", 0))
            fps = eval(video_stream.get("r_frame_rate", "30/1"))
            width = int(video_stream.get("width", 0))
            height = int(video_stream.get("height", 0))

            return {
                "duration": duration,
                "fps": fps,
                "width": width,
                "height": height,
                "video_stream": video_stream,
                "audio_stream": audio_stream,
                "format": info.get("format", {})
            }

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            return None

    def _apply_enhanced_timestamp_manipulation(self, input_path: str, output_path: str,
                                             normal_play_duration: float, actual_duration: float,
                                             display_duration: float, target_fps: float = 30.0,
                                             video_info = None) -> bool:
        """
        应用增强版时间戳操控（基于参考项目的方法）

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            normal_play_duration: 实际播放时长
            actual_duration: 实际播放时长（与normal_play_duration相同）
            display_duration: 播放器显示时长（原视频时长）
            target_fps: 目标帧率
        """
        try:
            import subprocess
            import os

            # 获取原视频信息
            if video_info:
                original_duration = video_info.get('duration', display_duration)
            else:
                original_duration = display_duration

            target_display_duration = display_duration
            actual_play_duration = actual_duration

            print(f"🎬 真正的时间戳欺骗:")
            print(f"   原视频时长: {original_duration}秒")
            print(f"   实际播放时长: {actual_play_duration}秒")
            print(f"   播放器显示时长: {target_display_duration}秒")

            # 参考项目的正确计算方法
            timestamp_scale = target_display_duration / actual_play_duration  # 时间戳拉伸倍数
            content_speed = original_duration / actual_play_duration  # 内容加速倍数

            print(f"   时间戳拉伸倍数: {timestamp_scale:.2f}x")
            print(f"   内容加速倍数: {content_speed:.2f}x")
            print(f"   效果: {original_duration}秒内容在{actual_play_duration}秒内播完，播放器显示{target_display_duration}秒")

            print(f"🔧 构建参考项目滤镜链...")

            # 检查是否有音频流
            has_audio = video_info and video_info.get("audio_stream") is not None

            # 构建参考项目的正确滤镜链
            print(f"🔧 构建参考项目滤镜链...")

            # 使用参考项目的线性快速跳跃方法
            video_filter = (
                f"[0:v]fps={target_fps},"
                f"setpts=PTS/{content_speed},"
                f"setpts=PTS*{timestamp_scale}[v_out]"
            )

            print(f"🎯 参考项目时间戳操控:")
            print(f"   线性快速跳跃，保持{target_fps}帧率")
            print(f"   内容加速{content_speed:.2f}倍")
            print(f"   时间戳拉伸{timestamp_scale:.2f}倍")
            print(f"   {actual_play_duration}秒播完{original_duration}秒内容")
            print(f"   播放器显示{target_display_duration}秒时长")

            if has_audio:
                # 音频处理：参考项目的方法
                if content_speed <= 2.0:
                    # 使用atempo（支持0.5-2.0倍速）
                    audio_filter = f"[0:a]atempo={content_speed},asetpts=PTS*{timestamp_scale}[a_out]"
                else:
                    # 使用多级atempo处理高倍速
                    atempo_stages = []
                    remaining_speed = content_speed
                    while remaining_speed > 2.0:
                        atempo_stages.append("atempo=2.0")
                        remaining_speed /= 2.0
                    if remaining_speed > 0.5:
                        atempo_stages.append(f"atempo={remaining_speed}")

                    audio_filter = f"[0:a]{','.join(atempo_stages)},asetpts=PTS*{timestamp_scale}[a_out]"

                filter_complex = f"{video_filter};{audio_filter}"

                # 构建FFmpeg命令（有音频）
                cmd = [
                    "ffmpeg", "-y",
                    "-i", input_path,
                    "-filter_complex", filter_complex,
                    "-map", "[v_out]",
                    "-map", "[a_out]",
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "23",
                    "-r", str(target_fps),  # 确保输出帧率
                    "-pix_fmt", "yuv420p",
                    "-c:a", "aac",
                    "-b:a", "128k",
                    "-ar", "44100",
                    "-movflags", "+faststart",
                    "-avoid_negative_ts", "make_zero",
                    output_path
                ]
            else:
                # 只有视频，无音频
                filter_complex = video_filter

                # 构建FFmpeg命令（无音频）
                cmd = [
                    "ffmpeg", "-y",
                    "-i", input_path,
                    "-filter_complex", filter_complex,
                    "-map", "[v_out]",
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "23",
                    "-r", str(target_fps),  # 确保输出帧率
                    "-pix_fmt", "yuv420p",
                    "-movflags", "+faststart",
                    "-avoid_negative_ts", "make_zero",
                    output_path
                ]

            print("⚡ 应用参考项目时间戳操控...")

            # 执行FFmpeg命令
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"✅ 参考项目时间戳操控成功!")
                print(f"   播放器将显示{target_display_duration}秒时长")
                print(f"   实际播放{actual_play_duration}秒完成")
                print(f"   内容加速{content_speed:.2f}倍，时间戳拉伸{timestamp_scale:.2f}倍")
                print(f"   进度条线性快速跳跃")
                return True
            else:
                print(f"❌ 参考项目时间戳操控失败:")
                print(f"   错误信息: {result.stderr}")
                return False

        except Exception as e:
            print(f"增强版时间戳操控失败: {str(e)}")
            return False

    def _apply_frame_duplication(self, video_path: str, actual_duration: float, display_duration: float) -> bool:
        """
        帧重复法：调整播放速度来改变显示时长
        """
        try:
            import subprocess
            import os

            temp_path = video_path + ".temp.mp4"

            # 计算播放速度因子
            speed_factor = actual_duration / display_duration

            print(f"🔧 使用帧重复法 (速度因子: {speed_factor:.3f})")

            cmd = [
                'ffmpeg', '-i', video_path,
                '-filter:v', f'setpts={1/speed_factor}*PTS',
                '-c:v', 'libx264', '-preset', 'fast',
                '-y', temp_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                os.replace(temp_path, video_path)
                print(f"✅ 帧重复法成功! 播放器将显示{display_duration}秒")
                return True
            else:
                print(f"❌ 帧重复法失败: {result.stderr}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                return False

        except Exception as e:
            print(f"帧重复法失败: {str(e)}")
            return False

    def _apply_silent_padding(self, video_path: str, actual_duration: float, display_duration: float) -> bool:
        """
        静默填充法：在视频后添加静默帧达到指定时长
        """
        try:
            import subprocess
            import os

            padding_duration = display_duration - actual_duration
            temp_padding = video_path + ".padding.mp4"
            temp_final = video_path + ".final.mp4"

            print(f"🔧 使用静默填充法 (填充{padding_duration:.2f}秒)")

            # 创建静默填充视频
            cmd1 = [
                'ffmpeg', '-f', 'lavfi',
                '-i', f'color=black:size=640x480:duration={padding_duration}:rate=30',
                '-c:v', 'libx264', '-preset', 'fast',
                '-y', temp_padding
            ]

            result1 = subprocess.run(cmd1, capture_output=True, text=True)
            if result1.returncode != 0:
                print(f"❌ 创建填充视频失败: {result1.stderr}")
                return False

            # 合并原视频和填充视频
            cmd2 = [
                'ffmpeg', '-i', video_path, '-i', temp_padding,
                '-filter_complex', '[0:v][1:v]concat=n=2:v=1[outv]',
                '-map', '[outv]',
                '-c:v', 'libx264', '-preset', 'fast',
                '-y', temp_final
            ]

            result2 = subprocess.run(cmd2, capture_output=True, text=True)

            # 清理临时文件
            if os.path.exists(temp_padding):
                os.remove(temp_padding)

            if result2.returncode == 0:
                os.replace(temp_final, video_path)
                print(f"✅ 静默填充法成功! 播放器将显示{display_duration}秒")
                return True
            else:
                print(f"❌ 视频合并失败: {result2.stderr}")
                if os.path.exists(temp_final):
                    os.remove(temp_final)
                return False

        except Exception as e:
            print(f"静默填充法失败: {str(e)}")
            return False

    def modify_video_duration_metadata(self, video_path: str, target_duration: float) -> bool:
        """
        修改视频元数据中的时长信息，让播放器显示指定时长

        Args:
            video_path: 视频文件路径
            target_duration: 目标显示时长

        Returns:
            是否成功
        """
        try:
            # 使用ffmpeg修改视频元数据
            temp_path = video_path + ".temp.mp4"

            # 构建ffmpeg命令，修改duration元数据
            cmd = [
                'ffmpeg', '-i', video_path,
                '-c', 'copy',  # 复制流，不重新编码
                '-metadata', f'duration={target_duration}',
                '-metadata', f'DURATION={int(target_duration * 1000000)}',  # 微秒
                '-y', temp_path
            ]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 替换原文件
                os.replace(temp_path, video_path)
                print(f"成功修改视频元数据，显示时长: {target_duration:.2f}秒")
                return True
            else:
                print(f"修改元数据失败: {result.stderr}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                return False

        except Exception as e:
            print(f"修改视频元数据失败: {str(e)}")
            return False

    def create_segmented_video(self, input_path: str, output_path: str,
                              normal_frame_count: int, speed_ratio: float,
                              original_info: Dict, total_display_duration: float, fps: int = 30) -> bool:
        """
        创建分段视频：前段正常播放，后段快速播放

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            normal_frame_count: 正常播放的帧数
            speed_ratio: 快速播放的速度比例
            original_info: 原始视频信息
            total_display_duration: 显示的总时长

        Returns:
            是否成功
        """
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            return False

        fps = original_info['fps']
        width = original_info['width']
        height = original_info['height']
        total_frames = original_info['frame_count']

        # 创建视频编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        current_frame = 0
        output_frame_count = 0

        with tqdm(total=total_frames, desc="创建分段时间戳欺骗视频") as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 计算当前时间戳
                current_time = current_frame / fps

                if current_frame < normal_frame_count:
                    # 正常播放阶段：直接输出帧
                    spoofed_frame = self.embed_timestamp_info(
                        frame, current_time, total_display_duration, "normal"
                    )
                    out.write(spoofed_frame)
                    output_frame_count += 1

                else:
                    # 快速播放阶段：按速度比例跳帧
                    if current_frame == normal_frame_count or \
                       (current_frame - normal_frame_count) % max(1, int(speed_ratio)) == 0:

                        # 计算欺骗后的时间戳
                        normal_duration = normal_frame_count / fps
                        fast_elapsed = (current_frame - normal_frame_count) / fps
                        spoofed_time = normal_duration + (fast_elapsed / speed_ratio)

                        spoofed_frame = self.embed_timestamp_info(
                            frame, spoofed_time, total_display_duration, "fast"
                        )
                        out.write(spoofed_frame)
                        output_frame_count += 1

                current_frame += 1
                pbar.update(1)

        cap.release()
        out.release()

        # 保存分段欺骗信息
        self.save_segmented_spoofing_info(input_path, output_path, normal_frame_count,
                                        speed_ratio, original_info, total_display_duration)

        print(f"输出视频帧数: {output_frame_count}")
        print(f"输出视频时长: {output_frame_count / fps:.2f}s")

        return True
    
    def embed_timestamp_info(self, frame: np.ndarray, spoofed_time: float,
                           total_duration: float, play_mode: str) -> np.ndarray:
        """
        在帧中嵌入时间戳欺骗信息

        Args:
            frame: 视频帧
            spoofed_time: 欺骗后的时间戳
            total_duration: 总时长
            play_mode: 播放模式 ("normal" 或 "fast")

        Returns:
            嵌入时间戳的帧
        """
        result = frame.copy()

        # 在帧的不可见区域嵌入时间戳信息
        timestamp_data = {
            'spoofed_time': spoofed_time,
            'total_duration': total_duration,
            'play_mode': play_mode,
            'progress': spoofed_time / total_duration if total_duration > 0 else 0
        }

        # 将时间戳信息编码到像素中（简化版本）
        # 在左上角几个像素中嵌入关键信息
        try:
            # 将时间信息编码为整数
            time_encoded = int(spoofed_time * 1000) % 16777216  # 24位
            duration_encoded = int(total_duration * 1000) % 16777216  # 24位

            # 嵌入到RGB通道的LSB中
            if result.shape[0] > 2 and result.shape[1] > 2:
                # 第一个像素存储当前时间
                result[0, 0, 0] = (result[0, 0, 0] & 0xF0) | ((time_encoded >> 20) & 0x0F)
                result[0, 0, 1] = (result[0, 0, 1] & 0xF0) | ((time_encoded >> 16) & 0x0F)
                result[0, 0, 2] = (result[0, 0, 2] & 0xF0) | ((time_encoded >> 12) & 0x0F)

                # 第二个像素存储总时长
                result[0, 1, 0] = (result[0, 1, 0] & 0xF0) | ((duration_encoded >> 20) & 0x0F)
                result[0, 1, 1] = (result[0, 1, 1] & 0xF0) | ((duration_encoded >> 16) & 0x0F)
                result[0, 1, 2] = (result[0, 1, 2] & 0xF0) | ((duration_encoded >> 12) & 0x0F)

                # 第三个像素存储播放模式标识
                mode_flag = 1 if play_mode == "fast" else 0
                result[0, 2, 0] = (result[0, 2, 0] & 0xFE) | mode_flag

        except Exception:
            pass  # 如果嵌入失败，返回原始帧

        return result

    def save_segmented_spoofing_info(self, input_path: str, output_path: str,
                                   normal_frame_count: int, speed_ratio: float,
                                   original_info: Dict, total_display_duration: float,
                                   generate_json: bool = False):
        """
        保存分段时间戳欺骗信息

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            normal_frame_count: 正常播放帧数
            speed_ratio: 快速播放速度比例
            original_info: 原始视频信息
            total_display_duration: 显示总时长
            generate_json: 是否生成JSON信息文件
        """
        normal_duration = normal_frame_count / original_info['fps']
        remaining_original = original_info['duration'] - normal_duration
        fast_display_duration = total_display_duration - normal_duration

        spoofing_info = {
            'input_video': input_path,
            'output_video': output_path,
            'spoofing_type': 'segmented_timestamp',
            'original_duration': original_info['duration'],
            'display_duration': total_display_duration,
            'normal_play_duration': normal_duration,
            'fast_play_duration': fast_display_duration,
            'fast_content_duration': remaining_original,
            'speed_ratio': speed_ratio,
            'normal_frame_count': normal_frame_count,
            'original_fps': original_info['fps'],
            'segments': [
                {
                    'type': 'normal',
                    'start_time': 0,
                    'end_time': normal_duration,
                    'speed_ratio': 1.0
                },
                {
                    'type': 'fast',
                    'start_time': normal_duration,
                    'end_time': total_display_duration,
                    'speed_ratio': speed_ratio,
                    'original_content_duration': remaining_original
                }
            ]
        }

        # 根据用户选择保存JSON信息文件
        if generate_json:
            info_file = output_path + '.spoofing.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(spoofing_info, f, indent=2, ensure_ascii=False)
            print(f"时间戳欺骗信息已保存到: {info_file}")

    def create_speed_adjusted_video(self, input_path: str, output_path: str,
                                  speed_ratio: float, original_info: Dict) -> bool:
        """
        通过调整播放速度创建时间戳欺骗视频

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            speed_ratio: 速度比例
            original_info: 原始视频信息

        Returns:
            是否成功
        """
        try:
            # 使用ffmpeg调整播放速度但保持时间戳
            cmd = [
                'ffmpeg', '-i', input_path,
                '-filter:v', f'setpts={1/speed_ratio}*PTS',
                '-r', str(original_info['fps']),
                '-c:v', 'libx264',
                '-preset', 'medium',
                output_path, '-y'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 后处理：修改元数据以显示原始时长
                self.modify_video_metadata(output_path, original_info)
                self.save_spoofing_info(input_path, output_path, speed_ratio, original_info)
                return True
            else:
                print(f"ffmpeg处理失败: {result.stderr}")
                return False

        except FileNotFoundError:
            print("ffmpeg未安装，使用备用方法")
            return self.create_hybrid_time_spoofed_video(input_path, output_path,
                                                        original_info['duration'] / 2)
        except Exception as e:
            print(f"速度调整失败: {str(e)}")
            return False

    def modify_video_metadata(self, video_path: str, original_info: Dict):
        """
        修改视频元数据以显示原始时长

        Args:
            video_path: 视频文件路径
            original_info: 原始视频信息
        """
        try:
            # 使用ffmpeg修改元数据
            metadata_cmd = [
                'ffmpeg', '-i', video_path,
                '-c', 'copy',
                '-metadata', f'duration={original_info["duration"]}',
                '-metadata', f'title=Original Duration: {original_info["duration"]:.2f}s',
                f'{video_path}.tmp', '-y'
            ]

            result = subprocess.run(metadata_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 替换原文件
                os.replace(f'{video_path}.tmp', video_path)
            else:
                # 清理临时文件
                if os.path.exists(f'{video_path}.tmp'):
                    os.remove(f'{video_path}.tmp')

        except Exception as e:
            print(f"修改元数据失败: {str(e)}")

    def save_spoofing_info(self, input_path: str, output_path: str,
                          speed_ratio: float, original_info: Dict, generate_json: bool = False):
        """
        保存时间戳欺骗信息

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            speed_ratio: 速度比例
            original_info: 原始视频信息
            generate_json: 是否生成JSON信息文件
        """
        spoofing_info = {
            'input_video': input_path,
            'output_video': output_path,
            'original_duration': original_info['duration'],
            'target_duration': original_info['duration'] / speed_ratio,
            'speed_ratio': speed_ratio,
            'original_fps': original_info['fps'],
            'original_frame_count': original_info['frame_count'],
            'spoofing_method': 'timestamp_control'
        }

        # 根据用户选择保存JSON信息文件
        if generate_json:
            info_file = output_path + '.spoofing.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(spoofing_info, f, indent=2, ensure_ascii=False)

    def get_video_info(self, video_path: str) -> Dict:
        """
        获取视频信息

        Args:
            video_path: 视频文件路径

        Returns:
            视频信息字典
        """
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")

        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        cap.release()

        return {
            'fps': fps,
            'frame_count': frame_count,
            'duration': duration,
            'width': width,
            'height': height,
            'file_size': os.path.getsize(video_path)
        }

    def time_to_seconds(self, time_str: str) -> float:
        """
        将时间字符串转换为秒数
        
        Args:
            time_str: 时间字符串，格式为 "HH:MM:SS" 或 "MM:SS" 或 "SS"
            
        Returns:
            秒数
        """
        parts = time_str.split(':')
        
        if len(parts) == 1:  # SS
            return float(parts[0])
        elif len(parts) == 2:  # MM:SS
            return float(parts[0]) * 60 + float(parts[1])
        elif len(parts) == 3:  # HH:MM:SS
            return float(parts[0]) * 3600 + float(parts[1]) * 60 + float(parts[2])
        else:
            raise ValueError(f"无效的时间格式: {time_str}")
    
    def seconds_to_time(self, seconds: float) -> str:
        """
        将秒数转换为时间字符串
        
        Args:
            seconds: 秒数
            
        Returns:
            时间字符串，格式为 "HH:MM:SS"
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    def cut_video(self, input_path: str, output_path: str, 
                  start_time: str, end_time: str) -> bool:
        """
        剪切视频片段
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            是否成功
        """
        try:
            start_seconds = self.time_to_seconds(start_time)
            end_seconds = self.time_to_seconds(end_time)
            
            if start_seconds >= end_seconds:
                raise ValueError("开始时间必须小于结束时间")
            
            # 使用OpenCV进行视频剪切
            cap = cv2.VideoCapture(input_path)
            
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {input_path}")
            
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 计算帧范围
            start_frame = int(start_seconds * fps)
            end_frame = int(end_seconds * fps)
            
            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 跳转到开始帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            current_frame = start_frame
            with tqdm(total=end_frame-start_frame, desc="剪切视频") as pbar:
                while current_frame < end_frame:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    
                    out.write(frame)
                    current_frame += 1
                    pbar.update(1)
            
            cap.release()
            out.release()
            
            return True
            
        except Exception as e:
            print(f"剪切视频失败: {str(e)}")
            return False
    
    def split_video(self, input_path: str, output_dir: str, 
                   split_points: List[str]) -> List[str]:
        """
        分割视频
        
        Args:
            input_path: 输入视频路径
            output_dir: 输出目录
            split_points: 分割时间点列表
            
        Returns:
            输出文件路径列表
        """
        try:
            # 获取视频信息
            video_info = self.get_video_info(input_path)
            duration = video_info['duration']
            
            # 转换分割点为秒数并排序
            split_seconds = [0.0]  # 开始时间
            for point in split_points:
                seconds = self.time_to_seconds(point)
                if 0 < seconds < duration:
                    split_seconds.append(seconds)
            split_seconds.append(duration)  # 结束时间
            split_seconds = sorted(list(set(split_seconds)))
            
            output_files = []
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            
            # 分割视频
            for i in range(len(split_seconds) - 1):
                start_time = self.seconds_to_time(split_seconds[i])
                end_time = self.seconds_to_time(split_seconds[i + 1])
                
                output_file = os.path.join(output_dir, f"{base_name}_part_{i+1:03d}.mp4")
                
                if self.cut_video(input_path, output_file, start_time, end_time):
                    output_files.append(output_file)
                else:
                    print(f"分割片段失败: {start_time} - {end_time}")
            
            return output_files
            
        except Exception as e:
            print(f"分割视频失败: {str(e)}")
            return []
    
    def merge_videos(self, input_paths: List[str], output_path: str) -> bool:
        """
        合并视频文件
        
        Args:
            input_paths: 输入视频路径列表
            output_path: 输出视频路径
            
        Returns:
            是否成功
        """
        try:
            if not input_paths:
                raise ValueError("输入视频列表不能为空")
            
            # 获取第一个视频的信息作为参考
            first_video_info = self.get_video_info(input_paths[0])
            fps = first_video_info['fps']
            width = first_video_info['width']
            height = first_video_info['height']
            
            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 逐个处理视频文件
            for i, video_path in enumerate(input_paths):
                print(f"正在处理第 {i+1}/{len(input_paths)} 个视频: {os.path.basename(video_path)}")
                
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    print(f"无法打开视频文件: {video_path}")
                    continue
                
                # 检查视频规格是否一致
                current_fps = cap.get(cv2.CAP_PROP_FPS)
                current_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                current_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                with tqdm(total=frame_count, desc=f"合并视频 {i+1}") as pbar:
                    while True:
                        ret, frame = cap.read()
                        if not ret:
                            break
                        
                        # 如果尺寸不一致，调整大小
                        if current_width != width or current_height != height:
                            frame = cv2.resize(frame, (width, height))
                        
                        out.write(frame)
                        pbar.update(1)
                
                cap.release()
            
            out.release()
            return True
            
        except Exception as e:
            print(f"合并视频失败: {str(e)}")
            return False
    
    def extract_audio(self, video_path: str, audio_path: str) -> bool:
        """
        提取视频音频
        
        Args:
            video_path: 视频文件路径
            audio_path: 音频输出路径
            
        Returns:
            是否成功
        """
        try:
            # 使用ffmpeg提取音频（如果可用）
            cmd = [
                'ffmpeg', '-i', video_path, 
                '-vn', '-acodec', 'copy', 
                audio_path, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except FileNotFoundError:
            print("ffmpeg未安装，无法提取音频")
            return False
        except Exception as e:
            print(f"提取音频失败: {str(e)}")
            return False
    
    def add_audio_to_video(self, video_path: str, audio_path: str, 
                          output_path: str) -> bool:
        """
        为视频添加音频
        
        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            # 使用ffmpeg合并音视频（如果可用）
            cmd = [
                'ffmpeg', '-i', video_path, '-i', audio_path,
                '-c:v', 'copy', '-c:a', 'aac',
                '-strict', 'experimental',
                output_path, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except FileNotFoundError:
            print("ffmpeg未安装，无法合并音视频")
            return False
        except Exception as e:
            print(f"合并音视频失败: {str(e)}")
            return False
    
    def get_frame_at_time(self, video_path: str, time_str: str) -> Optional[np.ndarray]:
        """
        获取指定时间的视频帧
        
        Args:
            video_path: 视频文件路径
            time_str: 时间字符串
            
        Returns:
            视频帧或None
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            seconds = self.time_to_seconds(time_str)
            frame_number = int(seconds * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()
            
            cap.release()
            
            return frame if ret else None
            
        except Exception as e:
            print(f"获取视频帧失败: {str(e)}")
            return None

    def batch_cut_videos(self, cut_tasks: List[Dict]) -> List[Dict]:
        """
        批量剪切视频

        Args:
            cut_tasks: 剪切任务列表，每个任务包含input_path, output_path, start_time, end_time

        Returns:
            处理结果列表
        """
        results = []

        for i, task in enumerate(cut_tasks):
            print(f"正在处理第 {i+1}/{len(cut_tasks)} 个剪切任务...")

            try:
                success = self.cut_video(
                    input_path=task.get('input_path', ''),
                    output_path=task.get('output_path', ''),
                    start_time=task.get('start_time', '0'),
                    end_time=task.get('end_time', '10')
                )

                results.append({
                    'task': task,
                    'success': success,
                    'error': None
                })

            except Exception as e:
                results.append({
                    'task': task,
                    'success': False,
                    'error': str(e)
                })

        return results

    def batch_split_videos(self, split_tasks: List[Dict]) -> List[Dict]:
        """
        批量分割视频

        Args:
            split_tasks: 分割任务列表，每个任务包含input_path, output_dir, split_points

        Returns:
            处理结果列表
        """
        results = []

        for i, task in enumerate(split_tasks):
            print(f"正在处理第 {i+1}/{len(split_tasks)} 个分割任务...")

            try:
                output_files = self.split_video(
                    input_path=task.get('input_path', ''),
                    output_dir=task.get('output_dir', ''),
                    split_points=task.get('split_points', [])
                )

                results.append({
                    'task': task,
                    'success': len(output_files) > 0,
                    'output_files': output_files,
                    'error': None
                })

            except Exception as e:
                results.append({
                    'task': task,
                    'success': False,
                    'output_files': [],
                    'error': str(e)
                })

        return results

    def batch_merge_videos(self, merge_tasks: List[Dict]) -> List[Dict]:
        """
        批量合并视频

        Args:
            merge_tasks: 合并任务列表，每个任务包含input_paths, output_path

        Returns:
            处理结果列表
        """
        results = []

        for i, task in enumerate(merge_tasks):
            print(f"正在处理第 {i+1}/{len(merge_tasks)} 个合并任务...")

            try:
                success = self.merge_videos(
                    input_paths=task.get('input_paths', []),
                    output_path=task.get('output_path', '')
                )

                results.append({
                    'task': task,
                    'success': success,
                    'error': None
                })

            except Exception as e:
                results.append({
                    'task': task,
                    'success': False,
                    'error': str(e)
                })

        return results

    def batch_create_time_spoofed_videos(self, video_tasks: List[Dict]) -> List[Dict]:
        """
        批量创建时间戳欺骗视频

        Args:
            video_tasks: 任务列表，每个任务包含input_path, output_path, normal_play_duration

        Returns:
            处理结果列表
        """
        results = []

        for i, task in enumerate(video_tasks):
            print(f"正在处理第 {i+1}/{len(video_tasks)} 个时间戳欺骗任务...")

            try:
                success = self.create_hybrid_time_spoofed_video(
                    input_path=task.get('input_path', ''),
                    output_path=task.get('output_path', ''),
                    normal_play_duration=task.get('normal_play_duration', 30),
                    total_display_duration=task.get('total_display_duration', None)
                )

                results.append({
                    'task': task,
                    'success': success,
                    'error': None
                })

            except Exception as e:
                results.append({
                    'task': task,
                    'success': False,
                    'error': str(e)
                })

        return results

    def create_time_spoofed_video_with_settings(self, input_path: str, output_path: str,
                                               settings: Dict) -> bool:
        """
        根据设置创建时间戳欺骗视频

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            settings: 设置字典，包含各种参数

        Returns:
            是否成功
        """
        try:
            normal_play_duration = settings.get('normal_play_duration', 30)
            total_display_duration = settings.get('total_display_duration', None)

            # 如果没有指定显示时长，使用原始时长
            if total_display_duration is None:
                original_info = self.get_video_info(input_path)
                total_display_duration = original_info['duration']

            return self.create_hybrid_time_spoofed_video(
                input_path, output_path, normal_play_duration, total_display_duration
            )

        except Exception as e:
            print(f"创建时间戳欺骗视频失败: {str(e)}")
            return False
