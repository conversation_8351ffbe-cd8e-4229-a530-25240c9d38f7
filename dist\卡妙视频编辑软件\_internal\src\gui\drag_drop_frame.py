#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专用的拖拽框架组件
解决拖拽功能实现问题
"""

import tkinter as tk
from tkinter import ttk
import os

# 尝试导入拖拽功能
try:
    from tkinterdnd2 import DND_FILES
    DND_AVAILABLE = True
except ImportError:
    DND_AVAILABLE = False

class DragDropFrame(ttk.Frame):
    """支持拖拽的框架组件"""
    
    def __init__(self, parent, drop_callback=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.drop_callback = drop_callback
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        if DND_AVAILABLE and self.drop_callback:
            try:
                self.drop_target_register(DND_FILES)
                self.dnd_bind('<<Drop>>', self._handle_drop)
                self.dnd_bind('<<DragEnter>>', self._on_drag_enter)
                self.dnd_bind('<<DragLeave>>', self._on_drag_leave)
                print(f"✅ 拖拽框架设置成功")
                return True
            except Exception as e:
                print(f"❌ 拖拽框架设置失败: {e}")
                return False
        return False
    
    def _handle_drop(self, event):
        """处理拖拽事件"""
        if self.drop_callback:
            self.drop_callback(event)
    
    def _on_drag_enter(self, event):
        """拖拽进入时的视觉反馈"""
        self.configure(style="DragEnter.TFrame")
    
    def _on_drag_leave(self, event):
        """拖拽离开时恢复原样"""
        self.configure(style="TFrame")

class DragDropListbox(tk.Listbox):
    """支持拖拽的列表框组件"""
    
    def __init__(self, parent, drop_callback=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.drop_callback = drop_callback
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        if DND_AVAILABLE and self.drop_callback:
            try:
                self.drop_target_register(DND_FILES)
                self.dnd_bind('<<Drop>>', self._handle_drop)
                self.dnd_bind('<<DragEnter>>', self._on_drag_enter)
                self.dnd_bind('<<DragLeave>>', self._on_drag_leave)
                print(f"✅ 拖拽列表框设置成功")
                return True
            except Exception as e:
                print(f"❌ 拖拽列表框设置失败: {e}")
                return False
        return False
    
    def _handle_drop(self, event):
        """处理拖拽事件"""
        if self.drop_callback:
            self.drop_callback(event)
    
    def _on_drag_enter(self, event):
        """拖拽进入时的视觉反馈"""
        self.configure(bg='lightblue')
    
    def _on_drag_leave(self, event):
        """拖拽离开时恢复原样"""
        self.configure(bg='white')

class DragDropEntry(ttk.Entry):
    """支持拖拽的输入框组件"""
    
    def __init__(self, parent, drop_callback=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.drop_callback = drop_callback
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        if DND_AVAILABLE and self.drop_callback:
            try:
                self.drop_target_register(DND_FILES)
                self.dnd_bind('<<Drop>>', self._handle_drop)
                self.dnd_bind('<<DragEnter>>', self._on_drag_enter)
                self.dnd_bind('<<DragLeave>>', self._on_drag_leave)
                print(f"✅ 拖拽输入框设置成功")
                return True
            except Exception as e:
                print(f"❌ 拖拽输入框设置失败: {e}")
                return False
        return False
    
    def _handle_drop(self, event):
        """处理拖拽事件"""
        if self.drop_callback:
            self.drop_callback(event)
    
    def _on_drag_enter(self, event):
        """拖拽进入时的视觉反馈"""
        self.configure(style="DragEnter.TEntry")
    
    def _on_drag_leave(self, event):
        """拖拽离开时恢复原样"""
        self.configure(style="TEntry")

def create_drag_drop_widget(widget_type, parent, drop_callback=None, **kwargs):
    """创建支持拖拽的控件的工厂函数"""
    
    if widget_type == "listbox":
        return DragDropListbox(parent, drop_callback, **kwargs)
    elif widget_type == "entry":
        return DragDropEntry(parent, drop_callback, **kwargs)
    elif widget_type == "frame":
        return DragDropFrame(parent, drop_callback, **kwargs)
    else:
        raise ValueError(f"不支持的控件类型: {widget_type}")

def setup_drag_styles(root):
    """设置拖拽相关的样式"""
    try:
        style = ttk.Style()
        
        # 拖拽进入时的样式
        style.configure("DragEnter.TFrame", background="lightblue")
        style.configure("DragEnter.TEntry", fieldbackground="lightblue")
        
        print("✅ 拖拽样式设置成功")
    except Exception as e:
        print(f"⚠️ 拖拽样式设置失败: {e}")

# 工具函数
def parse_dropped_files(event_data, root):
    """解析拖拽的文件列表"""
    try:
        files = root.tk.splitlist(event_data)
        return files
    except Exception as e:
        print(f"❌ 解析拖拽文件失败: {e}")
        return []

def scan_folder_for_files(folder_path, file_extensions=None):
    """扫描文件夹中的指定类型文件"""
    valid_files = []
    
    if file_extensions is None:
        # 默认支持的文件扩展名
        file_extensions = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp',  # 视频
            '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'  # 图片
        }
    
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext in file_extensions:
                    valid_files.append(file_path)
    except Exception as e:
        print(f"❌ 扫描文件夹失败: {e}")
    
    return valid_files

def is_valid_file_type(file_path, file_type):
    """检查文件类型是否有效"""
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_type == "video":
        return file_ext in {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp'}
    elif file_type == "image":
        return file_ext in {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
    else:
        return False
