#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复版打包脚本 - 解决所有依赖问题
"""

import os
import sys
import subprocess
from pathlib import Path

def create_final_spec():
    """创建最终修复版的规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(SPECPATH)

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[
        # 包含FFmpeg可执行文件
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffmpeg.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffprobe.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffplay.exe'), 'ffmpeg_bundle/bin'),
    ],
    datas=[
        # 包含FFmpeg预设和文档
        (str(project_root / 'ffmpeg_bundle' / 'presets'), 'ffmpeg_bundle/presets'),
        (str(project_root / 'ffmpeg_bundle' / 'doc'), 'ffmpeg_bundle/doc'),
        (str(project_root / 'ffmpeg_bundle' / 'LICENSE'), 'ffmpeg_bundle'),
        (str(project_root / 'ffmpeg_bundle' / 'README.txt'), 'ffmpeg_bundle'),
        # 包含源代码模块
        (str(project_root / 'src'), 'src'),
    ],
    hiddenimports=[
        # 核心模块
        'src.core.video_deduplication',
        'src.core.timestamp_control', 
        'src.core.image_to_video',
        'src.core.ab_video',
        'src.gui.enhanced_window',
        'src.gui.drag_drop_frame',
        'src.ffmpeg_setup',
        'src.timestamp_processor',
        'src.video_processor',
        # 第三方库
        'cv2',
        'numpy',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'matplotlib',
        'matplotlib.pyplot',
        'tqdm',
        'imageio',
        'imageio_ffmpeg',
        'tkinterdnd2',
        'ttkbootstrap',
        # scipy完整支持
        'scipy',
        'scipy.ndimage',
        'scipy._lib',
        'scipy._lib._array_api',
        'scipy._lib.array_api_compat',
        'scipy._lib.array_api_compat.numpy',
        'scipy.ndimage._support_alternative_backends',
        'scipy._lib._docscrape',
        # 标准库模块 - 不能排除
        'unittest',
        'unittest.mock',
        'unittest.case',
        'unittest.suite',
        'unittest.loader',
        'unittest.runner',
        'unittest.result',
        'unittest.signals',
        'unittest.util',
        'pydoc',
        'doctest',
        # numpy完整支持
        'numpy.testing',
        'numpy.testing._private',
        'numpy.testing._private.utils',
        # 其他必要模块
        'tempfile',
        'threading',
        'subprocess',
        'json',
        'hashlib',
        'pathlib',
        'glob',
        'random',
        'math',
        'typing',
        'collections',
        'collections.abc',
        'importlib',
        'importlib.util',
        'importlib.machinery',
        'platform',
        'warnings',
        'weakref',
        'inspect',
        'textwrap',
        're',
        'string',
        'io',
        'sys',
        'os',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 只排除真正不需要的测试模块
        'test',
        'tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VideoEditor_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 保留控制台以显示调试信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VideoEditor_Final',
)
'''
    
    with open('video_editor_final.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 最终修复版规格文件创建成功")

def build_final_executable():
    """构建最终修复版可执行文件"""
    print("🔨 开始构建最终修复版...")
    print("这次包含了所有必要的模块，应该能解决问题")
    
    try:
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "video_editor_final.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 最终修复版构建成功!")
            return True
        else:
            print("❌ 最终修复版构建失败!")
            print("\n错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {str(e)}")
        return False

def create_final_readme():
    """创建最终版使用说明"""
    readme_content = """# Video Editor - Final Fixed Version

## Fix Details
This version includes ALL necessary modules:
- scipy and all its dependencies
- unittest module (required by scipy)
- pydoc module (required by scipy._lib._docscrape)
- doctest module
- numpy.testing modules
- All standard library modules

## Usage
1. Double-click "VideoEditor_Final.exe"
2. First run may take a few seconds to load
3. If still having issues, run as administrator

## System Requirements
- Windows 7/8/10/11 (64-bit)
- At least 2GB RAM
- At least 1GB disk space

---
Final Fixed Version - All Dependencies Included
"""
    
    output_dir = Path("dist/VideoEditor_Final")
    if output_dir.exists():
        with open(output_dir / 'README_Final.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 最终版使用说明创建成功")

def main():
    """主函数"""
    print("🔧 Video Editor - Final Fix Tool")
    print("=" * 50)
    print("This version includes ALL necessary modules to fix the crash")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ Error: Please run this script in project root directory")
        return
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller is available")
    except ImportError:
        print("❌ Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return
    
    # 创建最终版规格文件
    create_final_spec()
    
    # 构建最终版可执行文件
    if build_final_executable():
        # 创建说明文件
        create_final_readme()
        
        print("\n" + "=" * 50)
        print("🎉 Final Fixed Version Build Complete!")
        print(f"📁 Output directory: {os.path.abspath('dist/VideoEditor_Final')}")
        print("📋 Files included:")
        print("   - VideoEditor_Final.exe (Final fixed version)")
        print("   - ffmpeg_bundle/ (Video processing tools)")
        print("   - src/ (Source code modules)")
        print("   - All dependencies and standard library modules")
        print("   - README_Final.txt (Usage instructions)")
        print("\n💡 This version should work without crashes!")
        print("🚀 Test the final version now!")
    else:
        print("❌ Final version build failed")

if __name__ == "__main__":
    main()
