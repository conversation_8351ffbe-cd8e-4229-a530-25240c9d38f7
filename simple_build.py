#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本 - 如果复杂版本失败可以使用这个
"""

import os
import sys
import subprocess

def simple_build():
    """简单的打包方法"""
    print("🔨 使用简化方法打包...")
    
    # 基本的PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",  # 创建目录而不是单文件
        "--windowed",  # 无控制台窗口
        "--name=卡妙视频编辑软件",
        "--add-data=src;src",  # 包含源代码
        "--add-data=ffmpeg_bundle;ffmpeg_bundle",  # 包含FFmpeg
        "--hidden-import=cv2",
        "--hidden-import=numpy", 
        "--hidden-import=PIL",
        "--hidden-import=tkinterdnd2",
        "--hidden-import=ttkbootstrap",
        "--hidden-import=matplotlib",
        "--hidden-import=tqdm",
        "--hidden-import=imageio",
        "--clean",  # 清理之前的构建
        "main.py"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 简化打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 简化打包失败: {e}")
        print("错误输出:", e.stderr)
        return False

def main():
    """主函数"""
    print("🎬 简化版打包工具")
    print("=" * 30)
    
    # 检查PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 执行打包
    if simple_build():
        print("\n✅ 打包完成!")
        print("📁 输出目录: dist/卡妙视频编辑软件/")
    else:
        print("\n❌ 打包失败")

if __name__ == "__main__":
    main()
