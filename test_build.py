#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包后的程序是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_executable():
    """测试可执行文件"""
    exe_path = Path("dist/卡妙视频编辑软件/卡妙视频编辑软件.exe")
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在")
        return False
    
    print("✅ 可执行文件存在")
    print(f"📁 文件路径: {exe_path.absolute()}")
    print(f"📊 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    return True

def test_dependencies():
    """测试依赖文件"""
    dist_dir = Path("dist/卡妙视频编辑软件")
    
    required_files = [
        "_internal/ffmpeg_bundle/bin/ffmpeg.exe",
        "_internal/ffmpeg_bundle/bin/ffprobe.exe", 
        "_internal/src",
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = dist_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    return True

def test_startup():
    """测试程序启动（快速测试）"""
    exe_path = Path("dist/卡妙视频编辑软件/卡妙视频编辑软件.exe")
    
    print("🚀 测试程序启动...")
    print("注意: 程序将在5秒后自动关闭")
    
    try:
        # 启动程序
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序启动成功")
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            # 进程已经结束，可能有错误
            stdout, stderr = process.communicate()
            print("❌ 程序启动失败")
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def create_test_report():
    """创建测试报告"""
    report = """# 打包测试报告

## 测试时间
{timestamp}

## 测试结果
- 可执行文件: {exe_test}
- 依赖文件: {dep_test}  
- 启动测试: {startup_test}

## 文件信息
- 输出目录: dist/卡妙视频编辑软件/
- 主程序: 卡妙视频编辑软件.exe
- 总体状态: {overall_status}

## 部署建议
{deployment_advice}
"""
    
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 执行测试
    exe_test = "✅ 通过" if test_executable() else "❌ 失败"
    dep_test = "✅ 通过" if test_dependencies() else "❌ 失败"
    startup_test = "✅ 通过" if test_startup() else "❌ 失败"
    
    overall_status = "✅ 可以部署" if all([
        "✅" in exe_test, "✅" in dep_test, "✅" in startup_test
    ]) else "❌ 需要修复"
    
    deployment_advice = """
1. 将整个 'dist/卡妙视频编辑软件' 文件夹复制到目标电脑
2. 双击运行 '卡妙视频编辑软件.exe'
3. 首次运行可能需要几秒钟加载时间
4. 如有问题请检查杀毒软件设置
""" if "✅" in overall_status else """
1. 检查打包过程是否有错误
2. 重新运行打包脚本
3. 查看详细错误日志
4. 尝试使用简化打包方法
"""
    
    report_content = report.format(
        timestamp=timestamp,
        exe_test=exe_test,
        dep_test=dep_test,
        startup_test=startup_test,
        overall_status=overall_status,
        deployment_advice=deployment_advice
    )
    
    with open("test_report.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print(f"\n📋 测试报告已保存: test_report.md")

def main():
    """主函数"""
    print("🧪 打包结果测试工具")
    print("=" * 30)
    
    if not os.path.exists("dist/卡妙视频编辑软件"):
        print("❌ 未找到打包输出目录")
        print("请先运行打包脚本")
        return
    
    print("开始测试打包结果...")
    print()
    
    # 创建测试报告
    create_test_report()
    
    print("\n" + "=" * 30)
    print("测试完成!")

if __name__ == "__main__":
    main()
