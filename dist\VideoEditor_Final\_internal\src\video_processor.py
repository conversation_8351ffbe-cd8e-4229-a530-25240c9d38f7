import threading
import queue
import time
from pathlib import Path
from typing import List, Optional
import subprocess
import tempfile
import cv2
import numpy as np

class VideoTask:
    """视频处理任务类"""
    def __init__(self, task_type: str, params: dict):
        self.task_type = task_type
        self.params = params
        self.status = "pending"
        self.progress: float = 0.0
        self.result = None
        self.error: Optional[str] = None

class VideoProcessor:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.running = False
        self.current_task = None
        self._start_processing_thread()
        
    def _start_processing_thread(self):
        """启动处理线程"""
        def process_loop():
            while True:
                try:
                    task = self.task_queue.get()
                    if task is None:  # 停止信号
                        break
                    
                    self.current_task = task
                    task.status = "processing"
                    
                    if task.task_type == "images_to_video":
                        self._process_images_to_video(task)
                    elif task.task_type == "ab_video":
                        self._process_ab_video(task)
                    
                    task.status = "completed" if not task.error else "failed"
                    
                except Exception as e:
                    if task:
                        task.error = str(e)
                        task.status = "failed"
                finally:
                    self.current_task = None
                    if task:
                        self.task_queue.task_done()

        self.running = True
        self.process_thread = threading.Thread(target=process_loop, daemon=True)
        self.process_thread.start()

    def _run_ffmpeg_silent(self, cmd: List[str]) -> bool:
        """静默运行 FFmpeg 命令"""
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=startupinfo
            )
            stdout, stderr = process.communicate()
            return process.returncode == 0
        except Exception as e:
            print(f"FFmpeg 执行错误: {e}")
            return False

    def _process_images_to_video(self, task: VideoTask):
        """处理图片转视频任务"""
        image_files = task.params.get("image_files", [])
        output_path = task.params.get("output_path")
        fps = task.params.get("fps", 30)
        
        if not image_files or not output_path:
            task.error = "缺少必要参数"
            return
            
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_dir = Path(temp_dir)
            
            # 获取第一张图片的尺寸
            first_img = cv2.imread(str(image_files[0]))
            if first_img is None:
                task.error = "无法读取第一张图片"
                return
                
            target_size = first_img.shape[:2]
            total_images = len(image_files)
            
            # 处理每张图片
            for i, img_path in enumerate(image_files):
                if not self.running:
                    task.error = "任务被取消"
                    return
                    
                img = cv2.imread(str(img_path))
                if img is not None:
                    # 调整大小
                    if img.shape[:2] != target_size:
                        img = cv2.resize(img, (target_size[1], target_size[0]))
                    
                    # 保存处理后的图片
                    temp_path = temp_dir / f"frame_{i:04d}.png"
                    cv2.imwrite(str(temp_path), img)
                    
                    # 更新进度
                    task.progress = (i + 1) / total_images * 100
            
            # 使用 FFmpeg 生成视频
            cmd = [
                "ffmpeg", "-y",
                "-framerate", str(fps),
                "-i", str(temp_dir / "frame_%04d.png"),
                "-c:v", "libx264",
                "-pix_fmt", "yuv420p",
                "-preset", "medium",
                "-crf", "23",
                str(output_path)
            ]
            
            return self._run_ffmpeg_silent(cmd)
    
    def _process_ab_video(self, task: VideoTask):
        """处理 AB 视频混合任务"""
        video_a = task.params.get("video_a")
        video_b = task.params.get("video_b")
        output_path = task.params.get("output_path")
        ratio = task.params.get("ratio", 0.5)
        
        if not all([video_a, video_b, output_path]):
            task.error = "缺少必要参数"
            return
            
        try:
            cap_a = cv2.VideoCapture(str(video_a))
            cap_b = cv2.VideoCapture(str(video_b))
            
            if not cap_a.isOpened() or not cap_b.isOpened():
                task.error = "无法打开视频文件"
                return
            
            # 获取视频参数
            width = int(cap_a.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap_a.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap_a.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
            
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_dir = Path(temp_dir)
                frame_count = 0
                
                while True:
                    if not self.running:
                        task.error = "任务被取消"
                        break
                        
                    ret_a, frame_a = cap_a.read()
                    ret_b, frame_b = cap_b.read()
                    
                    if not ret_a or not ret_b:
                        break
                    
                    # 调整第二个视频的大小以匹配第一个视频
                    frame_b = cv2.resize(frame_b, (width, height))
                    
                    # 混合两个帧
                    blended = cv2.addWeighted(frame_a, ratio, frame_b, 1-ratio, 0)
                    
                    # 保存帧
                    output_frame = temp_dir / f"frame_{frame_count:04d}.png"
                    cv2.imwrite(str(output_frame), blended)
                    frame_count += 1
                    
                    # 更新进度
                    task.progress = (frame_count / total_frames) * 100
                
                # 使用 FFmpeg 合成最终视频
                cmd = [
                    "ffmpeg", "-y",
                    "-framerate", str(fps),
                    "-i", str(temp_dir / "frame_%04d.png"),
                    "-c:v", "libx264",
                    "-pix_fmt", "yuv420p",
                    "-preset", "medium",
                    "-crf", "23",
                    str(output_path)
                ]
                
                return self._run_ffmpeg_silent(cmd)
                
        except Exception as e:
            print(f"AB视频处理错误: {e}")
            return False
        finally:
            if 'cap_a' in locals():
                cap_a.release()
            if 'cap_b' in locals():
                cap_b.release()
    
    def add_task(self, task_type: str, **params) -> VideoTask:
        """添加新任务到队列"""
        task = VideoTask(task_type, params)
        self.task_queue.put(task)
        return task
        
    def stop(self):
        """停止处理"""
        self.running = False
        self.task_queue.put(None)  # 发送停止信号
        if hasattr(self, 'process_thread'):
            self.process_thread.join()

    def get_task_status(self, task: VideoTask) -> dict:
        """获取任务状态"""
        return {
            'status': task.status,
            'progress': task.progress,
            'error': task.error
        }

# 创建全局视频处理器实例
video_processor = VideoProcessor()
