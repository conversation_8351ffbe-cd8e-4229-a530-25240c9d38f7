# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(SPECPATH)

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[
        # 将FFmpeg放在根目录，而不是_internal目录
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffmpeg.exe'), '.'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffprobe.exe'), '.'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffplay.exe'), '.'),
    ],
    datas=[
        # 同时在_internal目录也放一份
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffmpeg.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffprobe.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffplay.exe'), 'ffmpeg_bundle/bin'),
        # 包含FFmpeg预设和文档
        (str(project_root / 'ffmpeg_bundle' / 'presets'), 'ffmpeg_bundle/presets'),
        (str(project_root / 'ffmpeg_bundle' / 'doc'), 'ffmpeg_bundle/doc'),
        (str(project_root / 'ffmpeg_bundle' / 'LICENSE'), 'ffmpeg_bundle'),
        (str(project_root / 'ffmpeg_bundle' / 'README.txt'), 'ffmpeg_bundle'),
        # 包含源代码模块
        (str(project_root / 'src'), 'src'),
    ],
    hiddenimports=[
        # 核心模块
        'src.core.video_deduplication',
        'src.core.timestamp_control', 
        'src.core.image_to_video',
        'src.core.ab_video',
        'src.gui.enhanced_window',
        'src.gui.drag_drop_frame',
        'src.ffmpeg_setup',
        'src.timestamp_processor',
        'src.video_processor',
        # 第三方库
        'cv2',
        'numpy',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'matplotlib',
        'matplotlib.pyplot',
        'tqdm',
        'imageio',
        'imageio_ffmpeg',
        'tkinterdnd2',
        'ttkbootstrap',
        # scipy完整支持
        'scipy',
        'scipy.ndimage',
        'scipy._lib',
        'scipy._lib._array_api',
        'scipy._lib.array_api_compat',
        'scipy._lib.array_api_compat.numpy',
        'scipy.ndimage._support_alternative_backends',
        'scipy._lib._docscrape',
        # 标准库模块
        'unittest',
        'unittest.mock',
        'unittest.case',
        'unittest.suite',
        'unittest.loader',
        'unittest.runner',
        'unittest.result',
        'unittest.signals',
        'unittest.util',
        'pydoc',
        'doctest',
        'numpy.testing',
        'numpy.testing._private',
        'numpy.testing._private.utils',
        # 其他必要模块
        'tempfile',
        'threading',
        'subprocess',
        'json',
        'hashlib',
        'pathlib',
        'glob',
        'random',
        'math',
        'typing',
        'collections',
        'collections.abc',
        'importlib',
        'importlib.util',
        'importlib.machinery',
        'platform',
        'warnings',
        'weakref',
        'inspect',
        'textwrap',
        're',
        'string',
        'io',
        'sys',
        'os',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 只排除真正不需要的测试模块
        'test',
        'tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VideoEditor_Ultimate',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VideoEditor_Ultimate',
)
