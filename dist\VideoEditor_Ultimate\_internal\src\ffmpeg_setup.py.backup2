#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ffmpeg路径包装器 - 修复版
自动检测ffmpeg位置并设置环境变量
针对打包后的程序优化
"""

import os
import sys
from pathlib import Path

def setup_ffmpeg_path():
    """设置ffmpeg路径 - 修复版"""
    
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # 打包后的exe运行时
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller临时目录
            app_dir = Path(sys._MEIPASS)
        else:
            # 可执行文件所在目录
            app_dir = Path(sys.executable).parent
    else:
        # 开发环境
        app_dir = Path(__file__).parent.parent
    
    print(f"🔍 程序运行目录: {app_dir}")
    
    # ffmpeg可能的位置 - 针对打包后的结构
    ffmpeg_paths = [
        app_dir / "ffmpeg_bundle" / "bin",           # 打包后的ffmpeg位置
        app_dir / "_internal" / "ffmpeg_bundle" / "bin",  # PyInstaller内部目录
        app_dir / "ffmpeg" / "bin",                  # 便携版ffmpeg
        app_dir / "bin",                             # 简化目录结构
        Path("C:/ffmpeg/bin"),                       # 系统安装位置
        Path("C:/Program Files/ffmpeg/bin"),         # 程序文件夹
    ]
    
    print("🔍 搜索FFmpeg路径...")
    for path in ffmpeg_paths:
        print(f"   检查: {path}")
    
    # 查找ffmpeg
    ffmpeg_exe = None
    ffprobe_exe = None
    
    for path in ffmpeg_paths:
        print(f"🔍 检查路径: {path}")
        if path.exists():
            print(f"✅ 路径存在: {path}")
            ffmpeg_candidate = path / "ffmpeg.exe"
            ffprobe_candidate = path / "ffprobe.exe"
            
            print(f"   检查文件: {ffmpeg_candidate}")
            print(f"   检查文件: {ffprobe_candidate}")
            
            if ffmpeg_candidate.exists() and ffprobe_candidate.exists():
                ffmpeg_exe = str(ffmpeg_candidate)
                ffprobe_exe = str(ffprobe_candidate)
                
                # 添加到环境变量
                if str(path) not in os.environ.get("PATH", ""):
                    os.environ["PATH"] = str(path) + os.pathsep + os.environ.get("PATH", "")
                
                print(f"✅ 找到ffmpeg: {ffmpeg_exe}")
                print(f"✅ 找到ffprobe: {ffprobe_exe}")
                break
            else:
                print(f"❌ FFmpeg文件不存在于: {path}")
        else:
            print(f"❌ 路径不存在: {path}")
    
    if not ffmpeg_exe:
        print("⚠️ 未找到ffmpeg，某些功能可能无法使用")
        print("请确保ffmpeg.exe和ffprobe.exe在以下位置之一:")
        for path in ffmpeg_paths:
            print(f"  - {path}")
        
        # 尝试列出实际存在的目录
        print("\n🔍 实际存在的目录:")
        try:
            if getattr(sys, 'frozen', False):
                if hasattr(sys, '_MEIPASS'):
                    base_dir = Path(sys._MEIPASS)
                else:
                    base_dir = Path(sys.executable).parent
                
                print(f"基础目录: {base_dir}")
                if base_dir.exists():
                    for item in base_dir.iterdir():
                        if item.is_dir():
                            print(f"  📁 {item.name}")
                            if "ffmpeg" in item.name.lower():
                                print(f"     ⭐ 可能的FFmpeg目录!")
                                # 检查子目录
                                try:
                                    for subitem in item.iterdir():
                                        if subitem.is_dir():
                                            print(f"       📁 {subitem.name}")
                                except:
                                    pass
        except Exception as e:
            print(f"目录列举失败: {e}")
    
    return ffmpeg_exe, ffprobe_exe

# 自动设置ffmpeg路径
setup_ffmpeg_path()
