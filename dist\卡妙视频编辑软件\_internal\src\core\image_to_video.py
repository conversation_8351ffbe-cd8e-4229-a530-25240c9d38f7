#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图转视频功能模块
支持将图片序列转换为视频，包含多种转场效果
"""

import cv2
import numpy as np
import os
import random
from typing import List, Tuple, Optional, Dict
from PIL import Image, ImageEnhance, ImageFilter
from tqdm import tqdm
import glob

class ImageToVideoConverter:
    def __init__(self):
        """初始化图转视频转换器"""
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']

        # 所有可用的转场效果
        self.available_transitions = [
            "fade", "slide_left", "slide_right", "slide_up", "slide_down",
            "zoom_in", "zoom_out", "dissolve", "wipe_left", "wipe_right",
            "wipe_up", "wipe_down", "circle", "diamond", "blinds_horizontal",
            "blinds_vertical", "checkerboard", "spiral", "pixelate", "wave"
        ]
        
    def load_images(self, image_paths: List[str], target_size: Optional[Tuple[int, int]] = None) -> List[np.ndarray]:
        """
        加载图片列表
        
        Args:
            image_paths: 图片路径列表
            target_size: 目标尺寸 (width, height)
            
        Returns:
            图片数组列表
        """
        images = []
        
        for image_path in tqdm(image_paths, desc="加载图片"):
            try:
                # 使用PIL加载图片以支持更多格式
                pil_image = Image.open(image_path)
                
                # 转换为RGB模式
                if pil_image.mode != 'RGB':
                    pil_image = pil_image.convert('RGB')
                
                # 调整大小
                if target_size:
                    pil_image = pil_image.resize(target_size, Image.Resampling.LANCZOS)
                
                # 转换为OpenCV格式
                cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                images.append(cv_image)
                
            except Exception as e:
                print(f"加载图片失败: {image_path} - {str(e)}")
                continue
        
        return images
    
    def get_optimal_size(self, image_paths: List[str], orientation: str = "portrait") -> Tuple[int, int]:
        """
        获取最优的视频尺寸

        Args:
            image_paths: 图片路径列表
            orientation: 视频方向 ("portrait"竖屏, "landscape"横屏, "square"正方形, "auto"自动)

        Returns:
            最优尺寸 (width, height)
        """
        if orientation == "portrait":
            # 竖屏默认尺寸 (9:16)
            return (1080, 1920)
        elif orientation == "landscape":
            # 横屏默认尺寸 (16:9)
            return (1920, 1080)
        elif orientation == "square":
            # 正方形尺寸 (1:1)
            return (1080, 1080)

        # 自动模式：根据图片分析
        widths = []
        heights = []

        # 采样部分图片来确定尺寸
        sample_size = min(10, len(image_paths))
        sample_paths = image_paths[:sample_size]

        for image_path in sample_paths:
            try:
                with Image.open(image_path) as img:
                    widths.append(img.width)
                    heights.append(img.height)
            except Exception:
                continue

        if not widths or not heights:
            return (1080, 1920)  # 默认竖屏尺寸

        # 使用最常见的尺寸
        avg_width = int(np.median(widths))
        avg_height = int(np.median(heights))

        # 确保尺寸是偶数（视频编码要求）
        avg_width = avg_width if avg_width % 2 == 0 else avg_width + 1
        avg_height = avg_height if avg_height % 2 == 0 else avg_height + 1

        return (avg_width, avg_height)

    def get_standard_size(self, orientation: str = "portrait") -> Tuple[int, int]:
        """
        获取标准视频尺寸

        Args:
            orientation: 视频方向

        Returns:
            标准尺寸 (width, height)
        """
        if orientation == "portrait":
            # 竖屏标准尺寸选项
            return (1080, 1920)  # 9:16 (抖音、快手标准)
        elif orientation == "landscape":
            # 横屏标准尺寸选项
            return (1920, 1080)  # 16:9 (YouTube、B站标准)
        else:
            # 正方形
            return (1080, 1080)  # 1:1 (Instagram标准)
    
    def apply_transition(self, img1: np.ndarray, img2: np.ndarray,
                        transition_type: str, progress: float) -> np.ndarray:
        """
        应用转场效果

        Args:
            img1: 第一张图片
            img2: 第二张图片
            transition_type: 转场类型
            progress: 转场进度 (0-1)

        Returns:
            转场后的图片
        """
        if transition_type == "fade":
            return self.fade_transition(img1, img2, progress)
        elif transition_type == "slide_left":
            return self.slide_transition(img1, img2, progress, "left")
        elif transition_type == "slide_right":
            return self.slide_transition(img1, img2, progress, "right")
        elif transition_type == "slide_up":
            return self.slide_transition(img1, img2, progress, "up")
        elif transition_type == "slide_down":
            return self.slide_transition(img1, img2, progress, "down")
        elif transition_type == "zoom_in":
            return self.zoom_transition(img1, img2, progress, "in")
        elif transition_type == "zoom_out":
            return self.zoom_transition(img1, img2, progress, "out")
        elif transition_type == "dissolve":
            return self.dissolve_transition(img1, img2, progress)
        elif transition_type == "wipe_left":
            return self.wipe_transition(img1, img2, progress, "left")
        elif transition_type == "wipe_right":
            return self.wipe_transition(img1, img2, progress, "right")
        elif transition_type == "wipe_up":
            return self.wipe_transition(img1, img2, progress, "up")
        elif transition_type == "wipe_down":
            return self.wipe_transition(img1, img2, progress, "down")
        elif transition_type == "circle":
            return self.circle_transition(img1, img2, progress)
        elif transition_type == "diamond":
            return self.diamond_transition(img1, img2, progress)
        elif transition_type == "blinds_horizontal":
            return self.blinds_transition(img1, img2, progress, "horizontal")
        elif transition_type == "blinds_vertical":
            return self.blinds_transition(img1, img2, progress, "vertical")
        elif transition_type == "checkerboard":
            return self.checkerboard_transition(img1, img2, progress)
        elif transition_type == "spiral":
            return self.spiral_transition(img1, img2, progress)
        elif transition_type == "pixelate":
            return self.pixelate_transition(img1, img2, progress)
        elif transition_type == "wave":
            return self.wave_transition(img1, img2, progress)
        else:
            # 默认淡入淡出
            return self.fade_transition(img1, img2, progress)
    
    def fade_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """淡入淡出转场"""
        alpha = progress
        return cv2.addWeighted(img1, 1 - alpha, img2, alpha, 0)
    
    def slide_transition(self, img1: np.ndarray, img2: np.ndarray, 
                        progress: float, direction: str) -> np.ndarray:
        """滑动转场"""
        h, w = img1.shape[:2]
        result = np.zeros_like(img1)
        
        if direction == "left":
            offset = int(w * progress)
            result[:, :w-offset] = img1[:, offset:]
            result[:, w-offset:] = img2[:, :offset]
        elif direction == "right":
            offset = int(w * progress)
            result[:, offset:] = img1[:, :w-offset]
            result[:, :offset] = img2[:, w-offset:]
        elif direction == "up":
            offset = int(h * progress)
            result[:h-offset, :] = img1[offset:, :]
            result[h-offset:, :] = img2[:offset, :]
        elif direction == "down":
            offset = int(h * progress)
            result[offset:, :] = img1[:h-offset, :]
            result[:offset, :] = img2[h-offset:, :]
        
        return result
    
    def zoom_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float, direction: str = "in") -> np.ndarray:
        """缩放转场"""
        h, w = img1.shape[:2]

        if direction == "in":
            # 放大进入
            scale1 = 1 + progress * 0.5
            scale2 = 0.5 + progress * 0.5
        else:
            # 缩小退出
            scale1 = 1 - progress * 0.5
            scale2 = 1.5 - progress * 0.5

        # 缩放第一张图片
        new_h1 = int(h * scale1)
        new_w1 = int(w * scale1)

        if new_h1 > 0 and new_w1 > 0:
            img1_scaled = cv2.resize(img1, (new_w1, new_h1))

            # 居中裁剪或填充
            if scale1 >= 1:
                start_y = max(0, (new_h1 - h) // 2)
                start_x = max(0, (new_w1 - w) // 2)
                img1_cropped = img1_scaled[start_y:start_y+h, start_x:start_x+w]
            else:
                img1_cropped = np.zeros_like(img1)
                start_y = (h - new_h1) // 2
                start_x = (w - new_w1) // 2
                img1_cropped[start_y:start_y+new_h1, start_x:start_x+new_w1] = img1_scaled
        else:
            img1_cropped = img1

        # 缩放第二张图片
        new_h2 = int(h * scale2)
        new_w2 = int(w * scale2)

        if new_h2 > 0 and new_w2 > 0:
            img2_scaled = cv2.resize(img2, (new_w2, new_h2))

            # 居中放置或裁剪
            if scale2 >= 1:
                start_y = max(0, (new_h2 - h) // 2)
                start_x = max(0, (new_w2 - w) // 2)
                result = img2_scaled[start_y:start_y+h, start_x:start_x+w]
            else:
                result = np.zeros_like(img1)
                start_y = (h - new_h2) // 2
                start_x = (w - new_w2) // 2
                result[start_y:start_y+new_h2, start_x:start_x+new_w2] = img2_scaled
        else:
            result = img2

        # 混合
        alpha = progress
        return cv2.addWeighted(img1_cropped, 1 - alpha, result, alpha, 0)
    
    def dissolve_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """溶解转场"""
        # 创建随机掩码
        h, w = img1.shape[:2]
        mask = np.random.random((h, w)) < progress

        result = img1.copy()
        result[mask] = img2[mask]

        return result

    def wipe_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float, direction: str) -> np.ndarray:
        """擦除转场"""
        h, w = img1.shape[:2]
        result = img1.copy()

        if direction == "left":
            # 从左到右擦除
            wipe_pos = int(w * progress)
            result[:, :wipe_pos] = img2[:, :wipe_pos]
        elif direction == "right":
            # 从右到左擦除
            wipe_pos = int(w * (1 - progress))
            result[:, wipe_pos:] = img2[:, wipe_pos:]
        elif direction == "up":
            # 从上到下擦除
            wipe_pos = int(h * progress)
            result[:wipe_pos, :] = img2[:wipe_pos, :]
        elif direction == "down":
            # 从下到上擦除
            wipe_pos = int(h * (1 - progress))
            result[wipe_pos:, :] = img2[wipe_pos:, :]

        return result

    def circle_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """圆形转场"""
        h, w = img1.shape[:2]
        center_x, center_y = w // 2, h // 2
        max_radius = int(np.sqrt(center_x**2 + center_y**2))
        current_radius = int(max_radius * progress)

        # 创建圆形掩码
        y, x = np.ogrid[:h, :w]
        mask = (x - center_x)**2 + (y - center_y)**2 <= current_radius**2

        result = img1.copy()
        result[mask] = img2[mask]

        return result

    def diamond_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """菱形转场"""
        h, w = img1.shape[:2]
        center_x, center_y = w // 2, h // 2
        max_distance = center_x + center_y
        current_distance = int(max_distance * progress)

        # 创建菱形掩码
        y, x = np.ogrid[:h, :w]
        mask = np.abs(x - center_x) + np.abs(y - center_y) <= current_distance

        result = img1.copy()
        result[mask] = img2[mask]

        return result

    def blinds_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float, direction: str) -> np.ndarray:
        """百叶窗转场"""
        h, w = img1.shape[:2]
        result = img1.copy()

        if direction == "horizontal":
            # 水平百叶窗
            blind_height = 20
            num_blinds = h // blind_height

            for i in range(num_blinds):
                start_y = i * blind_height
                end_y = min(start_y + blind_height, h)

                # 每个百叶窗条的开启进度
                blind_progress = max(0, min(1, (progress - i * 0.1) * 2))
                if blind_progress > 0:
                    open_height = int(blind_height * blind_progress)
                    if open_height > 0:
                        result[start_y:start_y+open_height, :] = img2[start_y:start_y+open_height, :]
        else:
            # 垂直百叶窗
            blind_width = 20
            num_blinds = w // blind_width

            for i in range(num_blinds):
                start_x = i * blind_width
                end_x = min(start_x + blind_width, w)

                # 每个百叶窗条的开启进度
                blind_progress = max(0, min(1, (progress - i * 0.1) * 2))
                if blind_progress > 0:
                    open_width = int(blind_width * blind_progress)
                    if open_width > 0:
                        result[:, start_x:start_x+open_width] = img2[:, start_x:start_x+open_width]

        return result

    def checkerboard_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """棋盘转场"""
        h, w = img1.shape[:2]
        result = img1.copy()

        block_size = 30
        num_blocks_x = w // block_size
        num_blocks_y = h // block_size

        for i in range(num_blocks_y):
            for j in range(num_blocks_x):
                # 棋盘模式
                if (i + j) % 2 == 0:
                    block_progress = max(0, min(1, progress * 2))
                else:
                    block_progress = max(0, min(1, (progress - 0.5) * 2))

                if block_progress > 0:
                    start_y = i * block_size
                    end_y = min(start_y + block_size, h)
                    start_x = j * block_size
                    end_x = min(start_x + block_size, w)

                    # 混合当前块
                    alpha = block_progress
                    result[start_y:end_y, start_x:end_x] = cv2.addWeighted(
                        img1[start_y:end_y, start_x:end_x], 1 - alpha,
                        img2[start_y:end_y, start_x:end_x], alpha, 0
                    )

        return result

    def spiral_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """螺旋转场"""
        h, w = img1.shape[:2]
        center_x, center_y = w // 2, h // 2
        result = img1.copy()

        # 创建螺旋掩码
        y, x = np.ogrid[:h, :w]
        dx = x - center_x
        dy = y - center_y

        # 计算角度和距离
        angles = np.arctan2(dy, dx)
        distances = np.sqrt(dx**2 + dy**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)

        # 螺旋进度
        spiral_progress = (angles + np.pi) / (2 * np.pi) + distances / max_distance
        mask = spiral_progress <= progress * 2

        result[mask] = img2[mask]
        return result

    def pixelate_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """像素化转场"""
        h, w = img1.shape[:2]

        # 像素化程度随进度变化
        if progress < 0.5:
            # 前半段：第一张图片逐渐像素化
            pixel_size = int(1 + progress * 40)
            temp_img = img1.copy()

            # 缩小再放大实现像素化
            small_h, small_w = max(1, h // pixel_size), max(1, w // pixel_size)
            temp_small = cv2.resize(temp_img, (small_w, small_h), interpolation=cv2.INTER_NEAREST)
            pixelated = cv2.resize(temp_small, (w, h), interpolation=cv2.INTER_NEAREST)

            # 混合原图和像素化图
            alpha = progress * 2
            result = cv2.addWeighted(img1, 1 - alpha, pixelated, alpha, 0)
        else:
            # 后半段：像素化图片逐渐变为第二张图片
            pixel_size = int(40 - (progress - 0.5) * 40)
            pixel_size = max(1, pixel_size)

            temp_img = img2.copy()
            small_h, small_w = max(1, h // pixel_size), max(1, w // pixel_size)
            temp_small = cv2.resize(temp_img, (small_w, small_h), interpolation=cv2.INTER_NEAREST)
            pixelated = cv2.resize(temp_small, (w, h), interpolation=cv2.INTER_NEAREST)

            # 混合像素化图和第二张图
            alpha = (progress - 0.5) * 2
            result = cv2.addWeighted(pixelated, 1 - alpha, img2, alpha, 0)

        return result

    def wave_transition(self, img1: np.ndarray, img2: np.ndarray, progress: float) -> np.ndarray:
        """波浪转场"""
        h, w = img1.shape[:2]
        result = img1.copy()

        # 波浪参数
        wave_amplitude = 20
        wave_frequency = 0.02

        for y in range(h):
            # 计算波浪偏移
            wave_offset = int(wave_amplitude * np.sin(y * wave_frequency + progress * 10))
            wave_progress = (y + wave_offset) / h

            # 根据波浪进度决定显示哪张图片
            if wave_progress <= progress:
                result[y, :] = img2[y, :]

        return result
    
    def create_video_from_images(self, image_paths: List[str], output_path: str,
                                fps: float = 30.0, duration_per_image: float = 2.0,
                                transition_type: str = "fade", transition_duration: float = 0.5,
                                target_size: Optional[Tuple[int, int]] = None,
                                orientation: str = "portrait") -> bool:
        """
        从图片创建视频

        Args:
            image_paths: 图片路径列表
            output_path: 输出视频路径
            fps: 帧率
            duration_per_image: 每张图片显示时长（秒）
            transition_type: 转场类型
            transition_duration: 转场时长（秒）
            target_size: 目标尺寸
            orientation: 视频方向 ("portrait"竖屏, "landscape"横屏, "auto"自动)

        Returns:
            是否成功
        """
        try:
            if not image_paths:
                raise ValueError("图片列表不能为空")
            
            # 确定视频尺寸
            if target_size is None:
                target_size = self.get_optimal_size(image_paths, orientation)
            
            # 加载图片
            images = self.load_images(image_paths, target_size)
            
            if not images:
                raise ValueError("没有成功加载任何图片")
            
            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, target_size)
            
            # 计算帧数
            frames_per_image = int(duration_per_image * fps)
            transition_frames = int(transition_duration * fps)
            
            total_frames = len(images) * frames_per_image + (len(images) - 1) * transition_frames
            
            with tqdm(total=total_frames, desc="生成视频") as pbar:
                for i, image in enumerate(images):
                    # 显示当前图片
                    for _ in range(frames_per_image):
                        out.write(image)
                        pbar.update(1)
                    
                    # 添加转场效果（除了最后一张图片）
                    if i < len(images) - 1:
                        next_image = images[i + 1]

                        # 选择转场类型（支持随机转场）
                        current_transition = self.select_transition_type(transition_type, i)

                        for frame in range(transition_frames):
                            progress = frame / transition_frames
                            transition_frame = self.apply_transition(
                                image, next_image, current_transition, progress
                            )
                            out.write(transition_frame)
                            pbar.update(1)
            
            out.release()
            return True
            
        except Exception as e:
            print(f"创建视频失败: {str(e)}")
            return False
    
    def create_slideshow(self, image_directory: str, output_path: str,
                        pattern: str = "*", fps: float = 30.0,
                        duration_per_image: float = 3.0,
                        transition_type: str = "fade",
                        transition_duration: float = 1.0,
                        sort_by: str = "name",
                        orientation: str = "portrait") -> bool:
        """
        从目录创建幻灯片视频

        Args:
            image_directory: 图片目录
            output_path: 输出视频路径
            pattern: 文件匹配模式
            fps: 帧率
            duration_per_image: 每张图片显示时长
            transition_type: 转场类型
            transition_duration: 转场时长
            sort_by: 排序方式 ("name", "date", "size")
            orientation: 视频方向 ("portrait"竖屏, "landscape"横屏, "auto"自动)

        Returns:
            是否成功
        """
        try:
            # 查找图片文件
            image_paths = []
            for ext in self.supported_formats:
                pattern_path = os.path.join(image_directory, f"{pattern}{ext}")
                image_paths.extend(glob.glob(pattern_path))
                pattern_path = os.path.join(image_directory, f"{pattern}{ext.upper()}")
                image_paths.extend(glob.glob(pattern_path))
            
            if not image_paths:
                raise ValueError(f"在目录 {image_directory} 中没有找到图片文件")
            
            # 排序
            if sort_by == "name":
                image_paths.sort()
            elif sort_by == "date":
                image_paths.sort(key=lambda x: os.path.getmtime(x))
            elif sort_by == "size":
                image_paths.sort(key=lambda x: os.path.getsize(x))
            
            print(f"找到 {len(image_paths)} 张图片")
            
            # 创建视频
            return self.create_video_from_images(
                image_paths, output_path, fps, duration_per_image,
                transition_type, transition_duration, None, orientation
            )
            
        except Exception as e:
            print(f"创建幻灯片失败: {str(e)}")
            return False
    
    def add_text_overlay(self, image: np.ndarray, text: str, 
                        position: Tuple[int, int] = (50, 50),
                        font_scale: float = 1.0, color: Tuple[int, int, int] = (255, 255, 255),
                        thickness: int = 2) -> np.ndarray:
        """
        在图片上添加文字覆盖
        
        Args:
            image: 输入图片
            text: 文字内容
            position: 文字位置
            font_scale: 字体大小
            color: 文字颜色 (B, G, R)
            thickness: 文字粗细
            
        Returns:
            添加文字后的图片
        """
        result = image.copy()
        font = cv2.FONT_HERSHEY_SIMPLEX
        
        # 添加文字阴影
        cv2.putText(result, text, (position[0] + 2, position[1] + 2),
                   font, font_scale, (0, 0, 0), thickness + 1)
        
        # 添加文字
        cv2.putText(result, text, position, font, font_scale, color, thickness)

        return result

    def batch_create_videos(self, batch_configs: List[Dict]) -> List[Dict]:
        """
        批量创建视频

        Args:
            batch_configs: 批量配置列表，每个配置包含创建视频的参数

        Returns:
            处理结果列表
        """
        results = []

        for i, config in enumerate(batch_configs):
            print(f"正在处理第 {i+1}/{len(batch_configs)} 个任务...")

            try:
                success = self.create_video_from_images(
                    image_paths=config.get('image_paths', []),
                    output_path=config.get('output_path', ''),
                    fps=config.get('fps', 30.0),
                    duration_per_image=config.get('duration_per_image', 2.0),
                    transition_type=config.get('transition_type', 'fade'),
                    transition_duration=config.get('transition_duration', 0.5),
                    target_size=config.get('target_size', None)
                )

                results.append({
                    'config': config,
                    'success': success,
                    'error': None
                })

            except Exception as e:
                results.append({
                    'config': config,
                    'success': False,
                    'error': str(e)
                })

        return results

    def get_random_transition(self) -> str:
        """
        获取随机转场效果

        Returns:
            随机选择的转场效果名称
        """
        return random.choice(self.available_transitions)

    def select_transition_type(self, transition_type: str, image_index: int = 0) -> str:
        """
        选择转场类型，支持随机转场

        Args:
            transition_type: 用户指定的转场类型，如果是"random"则随机选择
            image_index: 当前图片索引，用于随机种子

        Returns:
            实际使用的转场效果名称
        """
        if transition_type == "random":
            # 为每个转场设置不同的随机种子，确保每次转场都不同
            random.seed(image_index * 12345)
            selected = self.get_random_transition()
            # 重置随机种子
            random.seed()
            return selected
        else:
            return transition_type
