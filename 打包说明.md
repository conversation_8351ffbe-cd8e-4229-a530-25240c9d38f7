# 卡妙视频编辑软件 - 打包成EXE说明

## 📦 打包方法

### 方法一：一键打包（推荐）
1. **双击运行** `打包成exe.bat`
2. 等待自动完成所有步骤
3. 在 `dist/卡妙视频编辑软件/` 目录找到可执行文件

### 方法二：Python脚本打包
```bash
# 安装打包依赖
pip install -r requirements_build.txt

# 执行打包
python build_exe.py
```

### 方法三：简化打包（备用方案）
如果上述方法失败，可以使用：
```bash
python simple_build.py
```

## 🔧 手动打包（高级用户）

如果自动打包失败，可以手动执行以下步骤：

### 1. 安装依赖
```bash
pip install pyinstaller>=5.13.0
pip install -r requirements.txt
```

### 2. 基础打包命令
```bash
pyinstaller --onedir --name="卡妙视频编辑软件" --add-data="src;src" --add-data="ffmpeg_bundle;ffmpeg_bundle" main.py
```

### 3. 完整打包命令（包含所有依赖）
```bash
pyinstaller ^
  --onedir ^
  --name="卡妙视频编辑软件" ^
  --add-data="src;src" ^
  --add-data="ffmpeg_bundle;ffmpeg_bundle" ^
  --hidden-import=cv2 ^
  --hidden-import=numpy ^
  --hidden-import=PIL ^
  --hidden-import=tkinterdnd2 ^
  --hidden-import=ttkbootstrap ^
  --hidden-import=matplotlib ^
  --hidden-import=tqdm ^
  --hidden-import=imageio ^
  --hidden-import=scipy ^
  --clean ^
  main.py
```

## 📁 输出结构

打包完成后，`dist/卡妙视频编辑软件/` 目录包含：

```
卡妙视频编辑软件/
├── 卡妙视频编辑软件.exe     # 主程序
├── _internal/               # 内部依赖文件
│   ├── ffmpeg_bundle/       # FFmpeg工具
│   ├── src/                 # 源代码模块
│   └── 其他依赖库...
└── README.txt               # 使用说明
```

## 🚀 部署到新电脑

1. **复制整个文件夹** `卡妙视频编辑软件/` 到目标电脑
2. **双击运行** `卡妙视频编辑软件.exe`
3. **首次运行** 可能需要几秒钟加载时间

## ⚠️ 常见问题

### 问题1：打包失败 - 模块未找到
**解决方案：**
```bash
# 重新安装所有依赖
pip uninstall -r requirements.txt -y
pip install -r requirements.txt
```

### 问题2：tkinterdnd2 打包失败
**解决方案：**
```bash
# 重新安装特定版本
pip uninstall tkinterdnd2 -y
pip install tkinterdnd2==0.4.2
```

### 问题3：FFmpeg 未正确包含
**解决方案：**
- 确保 `ffmpeg_bundle/bin/` 目录存在
- 检查 `ffmpeg.exe`, `ffprobe.exe` 文件是否存在

### 问题4：打包文件过大
**解决方案：**
```bash
# 使用UPX压缩（可选）
pyinstaller --upx-dir=upx_dir [其他参数] main.py
```

### 问题5：新电脑运行失败
**可能原因：**
- 缺少 Visual C++ 运行库
- 杀毒软件误报
- 权限不足

**解决方案：**
- 安装 Microsoft Visual C++ Redistributable
- 添加杀毒软件白名单
- 以管理员身份运行

## 💡 优化建议

### 减小文件大小
1. 排除不必要的模块：
```bash
--exclude-module=test --exclude-module=unittest
```

2. 使用UPX压缩：
```bash
--upx-dir=upx_dir
```

### 提高启动速度
1. 使用SSD硬盘
2. 关闭实时杀毒扫描（临时）
3. 预热运行一次

## 📋 系统要求

### 开发环境（打包）
- Windows 7/8/10/11
- Python 3.8+
- 至少 4GB 内存
- 至少 5GB 可用磁盘空间

### 目标环境（运行）
- Windows 7/8/10/11 (64位)
- 至少 2GB 内存
- 至少 1GB 可用磁盘空间

## 🔍 调试信息

如果遇到问题，可以：

1. **查看控制台输出**（保留console=True）
2. **检查日志文件**
3. **使用调试模式**：
```bash
pyinstaller --debug=all [其他参数] main.py
```

## 📞 技术支持

如果按照以上步骤仍然无法成功打包，请提供：
1. 错误信息截图
2. Python版本信息
3. 操作系统版本
4. 完整的错误日志
