#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB视频功能模块
实现视频替换功能，使平台检测为A视频，观众看到B视频
"""

import cv2
import numpy as np
import os
from typing import List, Dict, Tuple, Optional
from tqdm import tqdm
import hashlib
import json
import tempfile

class ABVideoProcessor:
    def __init__(self):
        """初始化AB视频处理器"""
        self.temp_dir = tempfile.mkdtemp()
        
    def get_video_metadata(self, video_path: str) -> Dict:
        """
        获取视频元数据
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频元数据字典
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        metadata = {
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'duration': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS),
            'file_size': os.path.getsize(video_path),
            'file_hash': self.calculate_file_hash(video_path)
        }
        
        cap.release()
        return metadata
    
    def calculate_file_hash(self, file_path: str, chunk_size: int = 8192) -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            chunk_size: 读取块大小
            
        Returns:
            文件MD5哈希值
        """
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def create_metadata_overlay(self, video_a_path: str, video_b_path: str,
                               output_path: str, overlay_method: str = "header", fps: int = 30,
                               generate_json: bool = False) -> bool:
        """
        创建带有A视频元数据的B视频
        
        Args:
            video_a_path: A视频路径（用于提取元数据）
            video_b_path: B视频路径（实际内容）
            output_path: 输出视频路径
            overlay_method: 覆盖方法 ("header", "footer", "invisible")
            fps: 输出帧率
            generate_json: 是否生成JSON映射文件

        Returns:
            是否成功
        """
        try:
            # 获取两个视频的元数据
            metadata_a = self.get_video_metadata(video_a_path)
            metadata_b = self.get_video_metadata(video_b_path)
            
            # 读取B视频
            cap_b = cv2.VideoCapture(video_b_path)
            if not cap_b.isOpened():
                raise ValueError(f"无法打开B视频: {video_b_path}")
            
            # 设置输出视频参数（使用A视频的某些元数据）
            fps = metadata_a['fps']
            width = metadata_b['width']
            height = metadata_b['height']
            
            # 创建视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            frame_count = 0
            total_frames = metadata_b['frame_count']
            
            with tqdm(total=total_frames, desc="处理AB视频") as pbar:
                while True:
                    ret, frame = cap_b.read()
                    if not ret:
                        break
                    
                    # 根据覆盖方法处理帧
                    if overlay_method == "header":
                        frame = self.add_header_metadata(frame, metadata_a, frame_count)
                    elif overlay_method == "footer":
                        frame = self.add_footer_metadata(frame, metadata_a, frame_count)
                    elif overlay_method == "invisible":
                        frame = self.add_invisible_metadata(frame, metadata_a, frame_count)
                    
                    out.write(frame)
                    frame_count += 1
                    pbar.update(1)
            
            cap_b.release()
            out.release()

            # 根据用户选择保存映射信息
            if generate_json:
                self.save_ab_mapping(video_a_path, video_b_path, output_path, metadata_a, metadata_b)

            return True
            
        except Exception as e:
            print(f"创建AB视频失败: {str(e)}")
            return False
    
    def add_header_metadata(self, frame: np.ndarray, metadata: Dict, frame_num: int) -> np.ndarray:
        """
        在帧顶部添加元数据信息
        
        Args:
            frame: 视频帧
            metadata: 元数据
            frame_num: 帧号
            
        Returns:
            处理后的帧
        """
        result = frame.copy()
        
        # 在顶部添加一个很小的透明条带包含元数据
        if frame_num % 100 == 0:  # 每100帧添加一次
            # 创建元数据字符串
            meta_str = f"FPS:{metadata['fps']:.1f}|HASH:{metadata['file_hash'][:8]}"
            
            # 在顶部1像素高度区域编码信息
            for i, char in enumerate(meta_str[:result.shape[1]//10]):
                if i < result.shape[1]:
                    # 使用ASCII值微调像素值
                    result[0, i, 0] = min(255, result[0, i, 0] + ord(char) % 10)
        
        return result
    
    def add_footer_metadata(self, frame: np.ndarray, metadata: Dict, frame_num: int) -> np.ndarray:
        """
        在帧底部添加元数据信息
        
        Args:
            frame: 视频帧
            metadata: 元数据
            frame_num: 帧号
            
        Returns:
            处理后的帧
        """
        result = frame.copy()
        
        # 在底部添加元数据
        if frame_num % 100 == 0:
            meta_str = f"DURATION:{metadata['duration']:.1f}|SIZE:{metadata['file_size']}"
            
            # 在底部1像素高度区域编码信息
            for i, char in enumerate(meta_str[:result.shape[1]//10]):
                if i < result.shape[1]:
                    result[-1, i, 2] = min(255, result[-1, i, 2] + ord(char) % 10)
        
        return result
    
    def add_invisible_metadata(self, frame: np.ndarray, metadata: Dict, frame_num: int) -> np.ndarray:
        """
        添加不可见的元数据信息（LSB隐写）
        
        Args:
            frame: 视频帧
            metadata: 元数据
            frame_num: 帧号
            
        Returns:
            处理后的帧
        """
        result = frame.copy()
        
        # 每1000帧在LSB中嵌入元数据
        if frame_num % 1000 == 0:
            meta_json = json.dumps({
                'fps': metadata['fps'],
                'hash': metadata['file_hash'][:16],
                'frame': frame_num
            })
            
            # 将字符串转换为二进制
            meta_binary = ''.join(format(ord(char), '08b') for char in meta_json)
            
            # 在帧的左上角区域嵌入数据
            data_index = 0
            for i in range(min(50, result.shape[0])):
                for j in range(min(50, result.shape[1])):
                    for k in range(3):  # RGB三个通道
                        if data_index < len(meta_binary):
                            # 修改LSB
                            result[i, j, k] = (result[i, j, k] & 0xFE) | int(meta_binary[data_index])
                            data_index += 1
                        else:
                            break
                    if data_index >= len(meta_binary):
                        break
                if data_index >= len(meta_binary):
                    break
        
        return result
    
    def save_ab_mapping(self, video_a_path: str, video_b_path: str, 
                       output_path: str, metadata_a: Dict, metadata_b: Dict):
        """
        保存AB视频映射信息
        
        Args:
            video_a_path: A视频路径
            video_b_path: B视频路径
            output_path: 输出视频路径
            metadata_a: A视频元数据
            metadata_b: B视频元数据
        """
        mapping_info = {
            'video_a': {
                'path': video_a_path,
                'metadata': metadata_a
            },
            'video_b': {
                'path': video_b_path,
                'metadata': metadata_b
            },
            'output': {
                'path': output_path,
                'created_time': os.path.getctime(output_path) if os.path.exists(output_path) else None
            }
        }
        
        mapping_file = output_path + '.mapping.json'
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_info, f, indent=2, ensure_ascii=False)
    
    def batch_create_ab_videos(self, ab_pairs: List[Dict], generate_json: bool = False) -> List[Dict]:
        """
        批量创建AB视频
        
        Args:
            ab_pairs: AB视频对列表，每个元素包含video_a, video_b, output_path等信息
            generate_json: 是否生成JSON映射文件

        Returns:
            处理结果列表
        """
        results = []
        
        for i, pair in enumerate(ab_pairs):
            print(f"正在处理第 {i+1}/{len(ab_pairs)} 个AB视频对...")
            
            try:
                success = self.create_metadata_overlay(
                    video_a_path=pair.get('video_a', ''),
                    video_b_path=pair.get('video_b', ''),
                    output_path=pair.get('output_path', ''),
                    overlay_method=pair.get('overlay_method', 'header'),
                    generate_json=generate_json
                )
                
                results.append({
                    'pair': pair,
                    'success': success,
                    'error': None
                })
                
            except Exception as e:
                results.append({
                    'pair': pair,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def extract_original_metadata(self, ab_video_path: str) -> Optional[Dict]:
        """
        从AB视频中提取原始A视频的元数据
        
        Args:
            ab_video_path: AB视频路径
            
        Returns:
            提取的元数据或None
        """
        try:
            # 尝试读取映射文件
            mapping_file = ab_video_path + '.mapping.json'
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mapping_info = json.load(f)
                return mapping_info.get('video_a', {}).get('metadata', {})
            
            # 如果没有映射文件，尝试从视频中提取
            return self.extract_metadata_from_frames(ab_video_path)
            
        except Exception as e:
            print(f"提取元数据失败: {str(e)}")
            return None
    
    def extract_metadata_from_frames(self, video_path: str) -> Optional[Dict]:
        """
        从视频帧中提取嵌入的元数据
        
        Args:
            video_path: 视频路径
            
        Returns:
            提取的元数据或None
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            # 尝试从不同位置提取元数据
            frame_count = 0
            while frame_count < 10000:  # 最多检查10000帧
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % 1000 == 0:
                    # 尝试从LSB中提取数据
                    metadata = self.extract_lsb_metadata(frame)
                    if metadata:
                        cap.release()
                        return metadata
                
                frame_count += 1
            
            cap.release()
            return None
            
        except Exception as e:
            print(f"从帧中提取元数据失败: {str(e)}")
            return None
    
    def extract_lsb_metadata(self, frame: np.ndarray) -> Optional[Dict]:
        """
        从帧的LSB中提取元数据
        
        Args:
            frame: 视频帧
            
        Returns:
            提取的元数据或None
        """
        try:
            # 从左上角区域提取LSB数据
            binary_data = ""
            for i in range(min(50, frame.shape[0])):
                for j in range(min(50, frame.shape[1])):
                    for k in range(3):
                        binary_data += str(frame[i, j, k] & 1)
            
            # 尝试将二进制数据转换为字符串
            chars = []
            for i in range(0, len(binary_data), 8):
                if i + 8 <= len(binary_data):
                    byte = binary_data[i:i+8]
                    char_code = int(byte, 2)
                    if 32 <= char_code <= 126:  # 可打印ASCII字符
                        chars.append(chr(char_code))
                    else:
                        break
            
            if chars:
                json_str = ''.join(chars)
                # 尝试找到JSON结束位置
                brace_count = 0
                json_end = -1
                for i, char in enumerate(json_str):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
                
                if json_end > 0:
                    return json.loads(json_str[:json_end])
            
            return None
            
        except Exception:
            return None
