#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ffmpeg路径包装器
自动检测ffmpeg位置并设置环境变量
"""

import os
import sys
from pathlib import Path

def setup_ffmpeg_path():
    """设置ffmpeg路径"""
    
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # 打包后的exe运行时
        app_dir = Path(sys._MEIPASS)
    else:
        # 开发环境
        app_dir = Path(__file__).parent
    
    # ffmpeg可能的位置
    ffmpeg_paths = [
        app_dir / "ffmpeg" / "bin",  # 便携版ffmpeg
        app_dir / "bin",             # 简化目录结构
        Path("C:/ffmpeg/bin"),       # 系统安装位置
        Path("C:/Program Files/ffmpeg/bin"),  # 程序文件夹
    ]
    
    # 查找ffmpeg
    ffmpeg_exe = None
    ffprobe_exe = None
    
    for path in ffmpeg_paths:
        if path.exists():
            ffmpeg_candidate = path / "ffmpeg.exe"
            ffprobe_candidate = path / "ffprobe.exe"
            
            if ffmpeg_candidate.exists() and ffprobe_candidate.exists():
                ffmpeg_exe = str(ffmpeg_candidate)
                ffprobe_exe = str(ffprobe_candidate)
                
                # 添加到环境变量
                if str(path) not in os.environ.get("PATH", ""):
                    os.environ["PATH"] = str(path) + os.pathsep + os.environ.get("PATH", "")
                
                print(f"✅ 找到ffmpeg: {ffmpeg_exe}")
                print(f"✅ 找到ffprobe: {ffprobe_exe}")
                break
    
    if not ffmpeg_exe:
        print("⚠️ 未找到ffmpeg，某些功能可能无法使用")
        print("请确保ffmpeg.exe和ffprobe.exe在以下位置之一:")
        for path in ffmpeg_paths:
            print(f"  - {path}")
    
    return ffmpeg_exe, ffprobe_exe

# 自动设置ffmpeg路径
setup_ffmpeg_path()
