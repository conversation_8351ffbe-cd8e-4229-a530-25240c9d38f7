@echo off
echo.
echo ========================================
echo   Video Editor - Final Fix
echo ========================================
echo.
echo This version includes ALL necessary modules
echo to fix the crash issue completely
echo.

echo Running final fix build...
python final_fix.py

if errorlevel 1 (
    echo.
    echo Final build failed
    echo Please check error messages
    pause
    exit /b 1
)

echo.
echo ========================================
echo      Final Fixed Version Complete!
echo ========================================
echo.
echo Output directory: dist\VideoEditor_Final\
echo Executable file: VideoEditor_Final.exe
echo.
echo This version includes:
echo    - All scipy dependencies
echo    - unittest module
echo    - pydoc module  
echo    - doctest module
echo    - All standard library modules
echo.
echo The crash issue should be completely fixed!
echo.

echo Open output directory now? (Y/N)
set /p choice=Choose: 
if /i "%choice%"=="Y" (
    explorer "dist\VideoEditor_Final"
)

pause
