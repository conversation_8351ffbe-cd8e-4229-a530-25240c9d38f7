#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频去重功能模块
通过添加特效、滤镜、转场、镜像等效果来修改视频，绕过平台原创检测
"""

import cv2
import numpy as np
import os
from typing import List, Tuple, Dict, Optional
import random
from tqdm import tqdm
import tempfile
import math
from scipy import ndimage
from PIL import Image, ImageFilter, ImageEnhance
import hashlib

class VideoDeduplicator:
    def __init__(self):
        """
        初始化视频去重器（原创检测绕过处理器）
        参考专业视频编辑软件的特效处理方案
        """
        self.temp_dir = tempfile.mkdtemp()

        # 专业级特效配置
        self.effects_config = {
            # 基础调色
            'brightness_range': (-40, 40),
            'contrast_range': (0.6, 1.4),
            'saturation_range': (0.7, 1.3),
            'gamma_range': (0.8, 1.2),
            'hue_shift_range': (-15, 15),

            # 模糊和锐化
            'blur_range': (0, 5),
            'motion_blur_range': (0, 8),
            'gaussian_blur_range': (0, 3),
            'sharpen_intensity': (0, 2),

            # 噪声和纹理
            'noise_intensity': (0, 20),
            'film_grain_intensity': (0, 15),
            'chromatic_aberration': (0, 3),

            # 几何变换
            'crop_margin': (0.01, 0.12),
            'rotation_range': (-2, 2),
            'scale_range': (0.95, 1.05),
            'perspective_distortion': (0, 0.02),

            # 时间效果
            'speed_range': (0.85, 1.15),
            'frame_skip_prob': 0.05,
            'frame_duplicate_prob': 0.03,

            # 颜色分级
            'color_temperature_range': (-500, 500),
            'tint_range': (-20, 20),
            'highlight_range': (-30, 30),
            'shadow_range': (-30, 30),

            # 特殊效果
            'vignette_intensity': (0, 0.3),
            'lens_distortion': (-0.1, 0.1),
            'chromatic_shift': (0, 2),
            'vintage_effect_prob': 0.2,
            'glitch_effect_prob': 0.1
        }

        # 预设效果组合
        self.effect_presets = {
            'light': {
                'effect_probability': 0.4,
                'intensity_multiplier': 0.6,
                'max_effects_per_frame': 2
            },
            'medium': {
                'effect_probability': 0.6,
                'intensity_multiplier': 1.0,
                'max_effects_per_frame': 3
            },
            'heavy': {
                'effect_probability': 0.8,
                'intensity_multiplier': 1.4,
                'max_effects_per_frame': 5
            },
            'extreme': {
                'effect_probability': 0.9,
                'intensity_multiplier': 1.8,
                'max_effects_per_frame': 7
            }
        }
        
    def apply_mirror_effect(self, frame: np.ndarray, mirror_type: str = "horizontal") -> np.ndarray:
        """
        应用镜像效果

        Args:
            frame: 输入帧
            mirror_type: 镜像类型 ("horizontal", "vertical", "both")

        Returns:
            镜像后的帧
        """
        if mirror_type == "horizontal":
            return cv2.flip(frame, 1)
        elif mirror_type == "vertical":
            return cv2.flip(frame, 0)
        elif mirror_type == "both":
            return cv2.flip(frame, -1)
        else:
            return frame

    def apply_color_filter(self, frame: np.ndarray, filter_type: str = "warm") -> np.ndarray:
        """
        应用颜色滤镜

        Args:
            frame: 输入帧
            filter_type: 滤镜类型 ("warm", "cool", "vintage", "bright", "dark")

        Returns:
            滤镜后的帧
        """
        if filter_type == "warm":
            # 暖色调滤镜
            frame[:, :, 0] = np.clip(frame[:, :, 0] * 0.8, 0, 255)  # 减少蓝色
            frame[:, :, 2] = np.clip(frame[:, :, 2] * 1.2, 0, 255)  # 增加红色
        elif filter_type == "cool":
            # 冷色调滤镜
            frame[:, :, 0] = np.clip(frame[:, :, 0] * 1.2, 0, 255)  # 增加蓝色
            frame[:, :, 2] = np.clip(frame[:, :, 2] * 0.8, 0, 255)  # 减少红色
        elif filter_type == "vintage":
            # 复古滤镜
            frame = cv2.addWeighted(frame, 0.8, np.full_like(frame, (20, 50, 80)), 0.2, 0)
        elif filter_type == "bright":
            # 亮度滤镜
            frame = cv2.convertScaleAbs(frame, alpha=1.2, beta=20)
        elif filter_type == "dark":
            # 暗色滤镜
            frame = cv2.convertScaleAbs(frame, alpha=0.8, beta=-20)

        return frame
    
    def apply_blur_effect(self, frame: np.ndarray, blur_intensity: int = 1) -> np.ndarray:
        """
        应用模糊效果

        Args:
            frame: 输入帧
            blur_intensity: 模糊强度 (1-10)

        Returns:
            模糊后的帧
        """
        if blur_intensity > 0:
            kernel_size = blur_intensity * 2 + 1
            return cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
        return frame
    
    def add_noise(self, frame: np.ndarray, noise_intensity: int = 10) -> np.ndarray:
        """
        添加噪声

        Args:
            frame: 输入帧
            noise_intensity: 噪声强度 (0-50)

        Returns:
            添加噪声后的帧
        """
        if noise_intensity > 0:
            noise = np.random.randint(-noise_intensity, noise_intensity, frame.shape, dtype=np.int16)
            noisy_frame = frame.astype(np.int16) + noise
            return np.clip(noisy_frame, 0, 255).astype(np.uint8)
        return frame

    def apply_crop_and_resize(self, frame: np.ndarray, crop_margin: float = 0.05) -> np.ndarray:
        """
        裁剪并调整大小

        Args:
            frame: 输入帧
            crop_margin: 裁剪边距比例 (0-0.2)

        Returns:
            裁剪调整后的帧
        """
        if crop_margin > 0:
            h, w = frame.shape[:2]
            margin_h = int(h * crop_margin)
            margin_w = int(w * crop_margin)

            # 裁剪
            cropped = frame[margin_h:h-margin_h, margin_w:w-margin_w]

            # 调整回原始大小
            return cv2.resize(cropped, (w, h))
        return frame
    
    def apply_random_effects(self, frame: np.ndarray, effect_intensity: str = "medium") -> np.ndarray:
        """
        随机应用多种效果

        Args:
            frame: 输入帧
            effect_intensity: 效果强度 ("light", "medium", "heavy")

        Returns:
            处理后的帧
        """
        result = frame.copy()

        # 根据强度设置参数
        if effect_intensity == "light":
            effect_prob = 0.3
            intensity_factor = 0.5
        elif effect_intensity == "medium":
            effect_prob = 0.5
            intensity_factor = 1.0
        else:  # heavy
            effect_prob = 0.7
            intensity_factor = 1.5

        # 随机应用颜色滤镜
        if random.random() < effect_prob:
            filters = ["warm", "cool", "vintage", "bright", "dark"]
            filter_type = random.choice(filters)
            result = self.apply_color_filter(result, filter_type)

        # 随机应用模糊
        if random.random() < effect_prob * 0.6:
            blur_intensity = int(random.uniform(1, 3) * intensity_factor)
            result = self.apply_blur_effect(result, blur_intensity)

        # 随机添加噪声
        if random.random() < effect_prob * 0.4:
            noise_intensity = int(random.uniform(5, 15) * intensity_factor)
            result = self.add_noise(result, noise_intensity)

        # 随机裁剪
        if random.random() < effect_prob * 0.3:
            crop_margin = random.uniform(0.02, 0.08) * intensity_factor
            result = self.apply_crop_and_resize(result, crop_margin)

        return result

    def process_video_file(self, input_path: str, output_path: str, effect_intensity: str = "medium", fps: int = 30) -> bool:
        """
        处理整个视频文件，添加特效绕过原创检测

        Args:
            input_path: 输入视频文件路径
            output_path: 输出视频文件路径
            effect_intensity: 效果强度 ("light", "medium", "heavy")

        Returns:
            bool: 处理是否成功
        """
        try:
            # 打开输入视频
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                print(f"错误：无法打开视频文件 {input_path}")
                return False

            # 获取视频属性
            original_fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # 设置输出视频编码器，使用指定的帧数
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not out.isOpened():
                print(f"错误：无法创建输出视频文件 {output_path}")
                cap.release()
                return False

            print(f"开始处理视频: {os.path.basename(input_path)}")
            print(f"总帧数: {total_frames}, 原始FPS: {original_fps}, 输出FPS: {fps}, 分辨率: {width}x{height}")

            # 处理每一帧
            frame_count = 0
            with tqdm(total=total_frames, desc="处理进度") as pbar:
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break

                    # 应用随机特效
                    processed_frame = self.apply_random_effects(frame, effect_intensity)

                    # 写入输出视频
                    out.write(processed_frame)

                    frame_count += 1
                    pbar.update(1)

            # 释放资源
            cap.release()
            out.release()

            print(f"✓ 视频处理完成: {output_path}")
            print(f"处理了 {frame_count} 帧")
            return True

        except Exception as e:
            print(f"处理视频时发生错误: {str(e)}")
            return False

    def analyze_video(self, video_path: str) -> Dict:
        """
        分析单个视频文件
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频分析结果
        """
        try:
            # 提取关键帧
            frames = self.extract_frames(video_path)
            
            # 计算帧哈希
            frame_hashes = []
            for frame in frames:
                hash_value = self.compute_frame_hash(frame)
                frame_hashes.append(hash_value)
            
            # 获取视频信息
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()
            
            return {
                'path': video_path,
                'frame_hashes': frame_hashes,
                'fps': fps,
                'duration': duration,
                'resolution': (width, height),
                'frame_count': frame_count,
                'file_size': os.path.getsize(video_path)
            }
            
        except Exception as e:
            return {
                'path': video_path,
                'error': str(e)
            }
    
    def find_duplicates(self, video_paths: List[str]) -> List[Tuple[str, str, float]]:
        """
        查找重复视频
        
        Args:
            video_paths: 视频文件路径列表
            
        Returns:
            重复视频对列表，每个元素为(视频1路径, 视频2路径, 相似度)
        """
        duplicates = []
        video_analyses = []
        
        # 分析所有视频
        print("正在分析视频文件...")
        for video_path in tqdm(video_paths, desc="分析视频"):
            analysis = self.analyze_video(video_path)
            if 'error' not in analysis:
                video_analyses.append(analysis)
            else:
                print(f"分析视频失败: {video_path} - {analysis['error']}")
        
        # 比较视频相似度
        print("正在比较视频相似度...")
        for i in range(len(video_analyses)):
            for j in range(i + 1, len(video_analyses)):
                video1 = video_analyses[i]
                video2 = video_analyses[j]
                
                similarity = self.compare_videos(video1, video2)
                
                if similarity >= self.similarity_threshold:
                    duplicates.append((video1['path'], video2['path'], similarity))
        
        return duplicates
    
    def compare_videos(self, video1: Dict, video2: Dict) -> float:
        """
        比较两个视频的相似度
        
        Args:
            video1: 第一个视频的分析结果
            video2: 第二个视频的分析结果
            
        Returns:
            相似度（0-1之间）
        """
        hashes1 = video1['frame_hashes']
        hashes2 = video2['frame_hashes']
        
        if not hashes1 or not hashes2:
            return 0.0
        
        # 计算帧哈希的平均相似度
        similarities = []
        
        # 使用动态规划找到最佳匹配
        min_len = min(len(hashes1), len(hashes2))
        max_len = max(len(hashes1), len(hashes2))
        
        # 如果长度差异太大，认为不相似
        if max_len / min_len > 2.0:
            return 0.0
        
        # 计算对应帧的相似度
        for i in range(min_len):
            idx1 = int(i * len(hashes1) / min_len)
            idx2 = int(i * len(hashes2) / min_len)
            
            similarity = self.calculate_similarity(hashes1[idx1], hashes2[idx2])
            similarities.append(similarity)
        
        # 返回平均相似度
        return np.mean(similarities) if similarities else 0.0
    
    def remove_duplicates(self, duplicates: List[Tuple[str, str, float]], 
                         keep_strategy: str = 'larger') -> List[str]:
        """
        移除重复视频
        
        Args:
            duplicates: 重复视频对列表
            keep_strategy: 保留策略 ('larger', 'smaller', 'newer', 'older')
            
        Returns:
            应该删除的文件路径列表
        """
        to_delete = set()
        
        for video1, video2, similarity in duplicates:
            if video1 in to_delete or video2 in to_delete:
                continue
            
            if keep_strategy == 'larger':
                size1 = os.path.getsize(video1)
                size2 = os.path.getsize(video2)
                to_delete.add(video2 if size1 > size2 else video1)
            
            elif keep_strategy == 'smaller':
                size1 = os.path.getsize(video1)
                size2 = os.path.getsize(video2)
                to_delete.add(video1 if size1 > size2 else video2)
            
            elif keep_strategy == 'newer':
                mtime1 = os.path.getmtime(video1)
                mtime2 = os.path.getmtime(video2)
                to_delete.add(video2 if mtime1 > mtime2 else video1)
            
            elif keep_strategy == 'older':
                mtime1 = os.path.getmtime(video1)
                mtime2 = os.path.getmtime(video2)
                to_delete.add(video1 if mtime1 > mtime2 else video2)
        
        return list(to_delete)

    def batch_analyze_directories(self, directories: List[str],
                                 file_extensions: List[str] = None) -> Dict:
        """
        批量分析多个目录中的视频文件

        Args:
            directories: 目录路径列表
            file_extensions: 支持的文件扩展名列表

        Returns:
            分析结果字典
        """
        if file_extensions is None:
            file_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']

        all_videos = []

        # 收集所有视频文件
        for directory in directories:
            if not os.path.exists(directory):
                print(f"目录不存在: {directory}")
                continue

            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in file_extensions):
                        video_path = os.path.join(root, file)
                        all_videos.append(video_path)

        print(f"找到 {len(all_videos)} 个视频文件")

        # 查找重复视频
        duplicates = self.find_duplicates(all_videos)

        # 按目录分组结果
        results_by_directory = {}
        for directory in directories:
            results_by_directory[directory] = {
                'videos': [v for v in all_videos if v.startswith(directory)],
                'duplicates': [d for d in duplicates if d[0].startswith(directory) or d[1].startswith(directory)]
            }

        return {
            'total_videos': len(all_videos),
            'total_duplicates': len(duplicates),
            'duplicates': duplicates,
            'by_directory': results_by_directory
        }

    def batch_remove_duplicates(self, directories: List[str],
                               keep_strategy: str = 'larger',
                               dry_run: bool = True) -> Dict:
        """
        批量移除多个目录中的重复视频

        Args:
            directories: 目录路径列表
            keep_strategy: 保留策略
            dry_run: 是否为试运行（不实际删除文件）

        Returns:
            处理结果字典
        """
        # 先分析找出重复文件
        analysis_result = self.batch_analyze_directories(directories)
        duplicates = analysis_result['duplicates']

        if not duplicates:
            return {
                'success': True,
                'message': '没有找到重复视频',
                'deleted_files': [],
                'saved_space': 0
            }

        # 确定要删除的文件
        to_delete = self.remove_duplicates(duplicates, keep_strategy)

        deleted_files = []
        saved_space = 0

        if not dry_run:
            # 实际删除文件
            for file_path in to_delete:
                try:
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    deleted_files.append(file_path)
                    saved_space += file_size
                    print(f"已删除: {file_path}")
                except Exception as e:
                    print(f"删除文件失败: {file_path} - {str(e)}")
        else:
            # 试运行，只计算会删除的文件和节省的空间
            for file_path in to_delete:
                try:
                    file_size = os.path.getsize(file_path)
                    deleted_files.append(file_path)
                    saved_space += file_size
                except Exception as e:
                    print(f"无法访问文件: {file_path} - {str(e)}")

        return {
            'success': True,
            'dry_run': dry_run,
            'total_duplicates': len(duplicates),
            'files_to_delete': len(to_delete),
            'deleted_files': deleted_files,
            'saved_space': saved_space,
            'saved_space_mb': saved_space / (1024 * 1024)
        }

    def generate_deduplication_report(self, analysis_result: Dict,
                                    output_file: str = None) -> str:
        """
        生成去重分析报告

        Args:
            analysis_result: 分析结果
            output_file: 输出文件路径

        Returns:
            报告内容
        """
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("视频去重分析报告")
        report_lines.append("=" * 60)
        report_lines.append(f"总视频文件数: {analysis_result['total_videos']}")
        report_lines.append(f"发现重复组数: {analysis_result['total_duplicates']}")
        report_lines.append("")

        if analysis_result['duplicates']:
            report_lines.append("重复视频详情:")
            report_lines.append("-" * 40)

            for i, (video1, video2, similarity) in enumerate(analysis_result['duplicates'], 1):
                report_lines.append(f"重复组 {i}:")
                report_lines.append(f"  视频1: {video1}")
                report_lines.append(f"  视频2: {video2}")
                report_lines.append(f"  相似度: {similarity:.3f}")

                # 添加文件大小信息
                try:
                    size1 = os.path.getsize(video1) / (1024 * 1024)
                    size2 = os.path.getsize(video2) / (1024 * 1024)
                    report_lines.append(f"  大小1: {size1:.2f} MB")
                    report_lines.append(f"  大小2: {size2:.2f} MB")
                except Exception:
                    pass

                report_lines.append("")

        # 按目录统计
        if 'by_directory' in analysis_result:
            report_lines.append("按目录统计:")
            report_lines.append("-" * 40)

            for directory, info in analysis_result['by_directory'].items():
                report_lines.append(f"目录: {directory}")
                report_lines.append(f"  视频文件数: {len(info['videos'])}")
                report_lines.append(f"  涉及重复数: {len(info['duplicates'])}")
                report_lines.append("")

        report_content = "\n".join(report_lines)

        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"报告已保存到: {output_file}")
            except Exception as e:
                print(f"保存报告失败: {str(e)}")

        return report_content
