@echo off
echo.
echo ========================================
echo   Video Editor - Fix Crash Issue
echo ========================================
echo.
echo This tool fixes EXE crash issue
echo No main program files will be modified
echo.

echo Running fixed version build...
python fix_build.py

if errorlevel 1 (
    echo.
    echo Build failed
    echo Please check error messages
    pause
    exit /b 1
)

echo.
echo ========================================
echo         Fixed Version Build Complete!
echo ========================================
echo.
echo Output directory: dist\VideoEditor_Fixed\
echo Executable file: VideoEditor_Fixed.exe
echo.
echo Fix details:
echo    - Fixed scipy module dependency issue
echo    - Fixed unittest module missing issue  
echo    - Fixed numpy.testing module issue
echo.
echo Now you can test the fixed version!
echo.

echo Open output directory now? (Y/N)
set /p choice=Choose: 
if /i "%choice%"=="Y" (
    explorer "dist\VideoEditor_Fixed"
)

pause
