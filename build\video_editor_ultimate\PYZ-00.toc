('C:\\Users\\<USER>\\Desktop\\视频编辑\\build\\video_editor_ultimate\\PYZ-00.pyz',
 [('PIL', 'F:\\Python\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'F:\\Python\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'F:\\Python\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'F:\\Python\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'F:\\Python\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'F:\\Python\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'F:\\Python\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'F:\\Python\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'F:\\Python\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'F:\\Python\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'F:\\Python\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'F:\\Python\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'F:\\Python\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'F:\\Python\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'F:\\Python\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'F:\\Python\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'F:\\Python\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'F:\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'F:\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'F:\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'F:\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'F:\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'F:\\Python\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'F:\\Python\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'F:\\Python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_markupbase', 'F:\\Python\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_opcode_metadata', 'F:\\Python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'F:\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'F:\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'F:\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'F:\\Python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'F:\\Python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'F:\\Python\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyrepl', 'F:\\Python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'F:\\Python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'F:\\Python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'F:\\Python\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'F:\\Python\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'F:\\Python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'F:\\Python\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'F:\\Python\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'F:\\Python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'F:\\Python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'F:\\Python\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'F:\\Python\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'F:\\Python\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'F:\\Python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'F:\\Python\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'F:\\Python\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'F:\\Python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'F:\\Python\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'F:\\Python\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'F:\\Python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'F:\\Python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'F:\\Python\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'F:\\Python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'F:\\Python\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins', 'F:\\Python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'F:\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'F:\\Python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'F:\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'F:\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'F:\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'F:\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'F:\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'F:\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'F:\\Python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'F:\\Python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'F:\\Python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'F:\\Python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'F:\\Python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'F:\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'F:\\Python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'F:\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'F:\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'F:\\Python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'F:\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'F:\\Python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'F:\\Python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'F:\\Python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'F:\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'F:\\Python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'F:\\Python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'F:\\Python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'F:\\Python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'F:\\Python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'F:\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'F:\\Python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'F:\\Python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'F:\\Python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'F:\\Python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'F:\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'F:\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'F:\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'F:\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'F:\\Python\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'F:\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('bs4', 'F:\\Python\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'F:\\Python\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'F:\\Python\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'F:\\Python\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'F:\\Python\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css', 'F:\\Python\\Lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('bs4.dammit', 'F:\\Python\\Lib\\site-packages\\bs4\\dammit.py', 'PYMODULE'),
  ('bs4.element',
   'F:\\Python\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'F:\\Python\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'F:\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'F:\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'F:\\Python\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'F:\\Python\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'F:\\Python\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'F:\\Python\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'F:\\Python\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'F:\\Python\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'F:\\Python\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'F:\\Python\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'F:\\Python\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'F:\\Python\\Lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'F:\\Python\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'F:\\Python\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'F:\\Python\\Lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'F:\\Python\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'F:\\Python\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'F:\\Python\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'F:\\Python\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'F:\\Python\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'F:\\Python\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'F:\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'F:\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'F:\\Python\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'F:\\Python\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'F:\\Python\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'F:\\Python\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'F:\\Python\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'F:\\Python\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'F:\\Python\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'F:\\Python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'F:\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'F:\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'F:\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'F:\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'F:\\Python\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'F:\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'F:\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('contourpy',
   'F:\\Python\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'F:\\Python\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'F:\\Python\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'F:\\Python\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'F:\\Python\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'F:\\Python\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'F:\\Python\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'F:\\Python\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'F:\\Python\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'F:\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'F:\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'F:\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'F:\\Python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'F:\\Python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'F:\\Python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'F:\\Python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'F:\\Python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'F:\\Python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'F:\\Python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'F:\\Python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'F:\\Python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'F:\\Python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('cycler', 'F:\\Python\\Lib\\site-packages\\cycler\\__init__.py', 'PYMODULE'),
  ('dataclasses', 'F:\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'F:\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'F:\\Python\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'F:\\Python\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'F:\\Python\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'F:\\Python\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'F:\\Python\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'F:\\Python\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'F:\\Python\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'F:\\Python\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'F:\\Python\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'F:\\Python\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'F:\\Python\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'F:\\Python\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'F:\\Python\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'F:\\Python\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'F:\\Python\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'F:\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'F:\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'F:\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'F:\\Python\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'F:\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'F:\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'F:\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'F:\\Python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'F:\\Python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'F:\\Python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'F:\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'F:\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'F:\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'F:\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'F:\\Python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'F:\\Python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'F:\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'F:\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'F:\\Python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'F:\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'F:\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'F:\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'F:\\Python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'F:\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'F:\\Python\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'F:\\Python\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'F:\\Python\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'F:\\Python\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'F:\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'F:\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'F:\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'F:\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'F:\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'F:\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'F:\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'F:\\Python\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'F:\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'F:\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'F:\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'F:\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'F:\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'F:\\Python\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'F:\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'F:\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'F:\\Python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'F:\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('imageio',
   'F:\\Python\\Lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.config',
   'F:\\Python\\Lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'F:\\Python\\Lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'F:\\Python\\Lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('imageio.core.format',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.core.request',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.core.util',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'F:\\Python\\Lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.plugins',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'F:\\Python\\Lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'F:\\Python\\Lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.v2', 'F:\\Python\\Lib\\site-packages\\imageio\\v2.py', 'PYMODULE'),
  ('imageio.v3', 'F:\\Python\\Lib\\site-packages\\imageio\\v3.py', 'PYMODULE'),
  ('imageio_ffmpeg',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg._definitions',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\_definitions.py',
   'PYMODULE'),
  ('imageio_ffmpeg._io',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\_io.py',
   'PYMODULE'),
  ('imageio_ffmpeg._parsing',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\_parsing.py',
   'PYMODULE'),
  ('imageio_ffmpeg._utils',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\_utils.py',
   'PYMODULE'),
  ('imageio_ffmpeg.binaries',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\binaries\\__init__.py',
   'PYMODULE'),
  ('importlib', 'F:\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'F:\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'F:\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'F:\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'F:\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'F:\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'F:\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'F:\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'F:\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'F:\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'F:\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'F:\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'F:\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'F:\\Python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'F:\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'F:\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'F:\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'F:\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'F:\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'F:\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'F:\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'F:\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'F:\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'F:\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2', 'F:\\Python\\Lib\\site-packages\\jinja2\\__init__.py', 'PYMODULE'),
  ('jinja2._identifier',
   'F:\\Python\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'F:\\Python\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'F:\\Python\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'F:\\Python\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'F:\\Python\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'F:\\Python\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'F:\\Python\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'F:\\Python\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'F:\\Python\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext', 'F:\\Python\\Lib\\site-packages\\jinja2\\ext.py', 'PYMODULE'),
  ('jinja2.filters',
   'F:\\Python\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'F:\\Python\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'F:\\Python\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'F:\\Python\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'F:\\Python\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'F:\\Python\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'F:\\Python\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'F:\\Python\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'F:\\Python\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'F:\\Python\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'F:\\Python\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'F:\\Python\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'F:\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'F:\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'F:\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'F:\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('kiwisolver',
   'F:\\Python\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'F:\\Python\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging', 'F:\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'F:\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'F:\\Python\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'F:\\Python\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes', 'F:\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'F:\\Python\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'F:\\Python\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'F:\\Python\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'F:\\Python\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'F:\\Python\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'F:\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'F:\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'F:\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'F:\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'F:\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'F:\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'F:\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'F:\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'F:\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'F:\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'F:\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'F:\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'F:\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'F:\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'F:\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'F:\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'F:\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'F:\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'F:\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'F:\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'F:\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'F:\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'F:\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'F:\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'F:\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'F:\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'F:\\Python\\Lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'F:\\Python\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'F:\\Python\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'F:\\Python\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'F:\\Python\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'F:\\Python\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'F:\\Python\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'F:\\Python\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'F:\\Python\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'F:\\Python\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'F:\\Python\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'F:\\Python\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'F:\\Python\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'F:\\Python\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'F:\\Python\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'F:\\Python\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'F:\\Python\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'F:\\Python\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'F:\\Python\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__main__',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\__main__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py._src_pyf',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\_src_pyf.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'F:\\Python\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'F:\\Python\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'F:\\Python\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'F:\\Python\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'F:\\Python\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'F:\\Python\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'F:\\Python\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'F:\\Python\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'F:\\Python\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'F:\\Python\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'F:\\Python\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'F:\\Python\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'F:\\Python\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'F:\\Python\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'F:\\Python\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'F:\\Python\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'F:\\Python\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'F:\\Python\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'F:\\Python\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'F:\\Python\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'F:\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'F:\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'F:\\Python\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'F:\\Python\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'F:\\Python\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'F:\\Python\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'F:\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'F:\\Python\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'F:\\Python\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'F:\\Python\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'F:\\Python\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'F:\\Python\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'F:\\Python\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'F:\\Python\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'F:\\Python\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'F:\\Python\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'F:\\Python\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'F:\\Python\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'F:\\Python\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'F:\\Python\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'F:\\Python\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'F:\\Python\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'F:\\Python\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'F:\\Python\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'F:\\Python\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas', 'F:\\Python\\Lib\\site-packages\\pandas\\__init__.py', 'PYMODULE'),
  ('pandas._config',
   'F:\\Python\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'F:\\Python\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'F:\\Python\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'F:\\Python\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'F:\\Python\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'F:\\Python\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'F:\\Python\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'F:\\Python\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'F:\\Python\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'F:\\Python\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'F:\\Python\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'F:\\Python\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'F:\\Python\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'F:\\Python\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'F:\\Python\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'F:\\Python\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'F:\\Python\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'F:\\Python\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'F:\\Python\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'F:\\Python\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'F:\\Python\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'F:\\Python\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'F:\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'F:\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'F:\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'F:\\Python\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'F:\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'F:\\Python\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'F:\\Python\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'F:\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'F:\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'F:\\Python\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'F:\\Python\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'F:\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil', 'F:\\Python\\Lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._common',
   'F:\\Python\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'F:\\Python\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'F:\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'F:\\Python\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'F:\\Python\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'F:\\Python\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'F:\\Python\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'F:\\Python\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'F:\\Python\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'F:\\Python\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'F:\\Python\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'F:\\Python\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'F:\\Python\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'F:\\Python\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'F:\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'F:\\Python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'F:\\Python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyparsing',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'F:\\Python\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pytz', 'F:\\Python\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.exceptions',
   'F:\\Python\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy', 'F:\\Python\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.tzfile',
   'F:\\Python\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'F:\\Python\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'F:\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'F:\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'F:\\Python\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'F:\\Python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'F:\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('scipy', 'F:\\Python\\Lib\\site-packages\\scipy\\__init__.py', 'PYMODULE'),
  ('scipy.__config__',
   'F:\\Python\\Lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'F:\\Python\\Lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib._array_api_compat_vendor',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_array_api_compat_vendor.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._sparse',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_sparse.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._fft',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.fft',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\fft.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._typing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._delegation',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_delegation.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._at',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_at.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._funcs',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._lazy',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_lazy.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._compat',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_compat.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._helpers',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._lib._utils._typing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\_lib\\_utils\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra.testing',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\array_api_extra\\testing.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.pyprima',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobyla',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobyla.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.cobylb',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\cobylb.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.geometry',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.initialize',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\initialize.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.trustregion',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\trustregion.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.cobyla.update',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\cobyla\\update.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._bounds',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_bounds.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._linear_constraints',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_linear_constraints.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._nonlinear_constraints',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_nonlinear_constraints.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common._project',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\_project.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.checkbreak',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\checkbreak.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.consts',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\consts.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.evaluate',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\evaluate.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.history',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\history.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.infos',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\infos.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.linalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.message',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\message.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.powalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\powalg.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.preproc',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\preproc.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.present',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\present.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.ratio',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\ratio.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.redrho',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\redrho.py',
   'PYMODULE'),
  ('scipy._lib.pyprima.common.selectx',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\pyprima\\common\\selectx.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.constants',
   'F:\\Python\\Lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'F:\\Python\\Lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'F:\\Python\\Lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'F:\\Python\\Lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'F:\\Python\\Lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.integrate',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._elementwise',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_elementwise.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.elementwise',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\elementwise.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.stats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._finite_differences',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_finite_differences.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._quantile',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_quantile.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'F:\\Python\\Lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets', 'F:\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'F:\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'F:\\Python\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'F:\\Python\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'F:\\Python\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'F:\\Python\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'F:\\Python\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'F:\\Python\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'F:\\Python\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'F:\\Python\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'F:\\Python\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'F:\\Python\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'F:\\Python\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'F:\\Python\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'F:\\Python\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'F:\\Python\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'F:\\Python\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'F:\\Python\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'F:\\Python\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'F:\\Python\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'F:\\Python\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'F:\\Python\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'F:\\Python\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'F:\\Python\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'F:\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'F:\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'F:\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'F:\\Python\\Lib\\site.py', 'PYMODULE'),
  ('six', 'F:\\Python\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'F:\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'F:\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('soupsieve',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'F:\\Python\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlalchemy',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3', 'F:\\Python\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'F:\\Python\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'F:\\Python\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'F:\\Python\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('src', 'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\__init__.py', 'PYMODULE'),
  ('src.core',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__init__.py',
   'PYMODULE'),
  ('src.core.ab_video',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\ab_video.py',
   'PYMODULE'),
  ('src.core.image_to_video',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\image_to_video.py',
   'PYMODULE'),
  ('src.core.timestamp_control',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\timestamp_control.py',
   'PYMODULE'),
  ('src.core.video_deduplication',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\video_deduplication.py',
   'PYMODULE'),
  ('src.ffmpeg_setup',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\ffmpeg_setup.py',
   'PYMODULE'),
  ('src.gui',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\__init__.py',
   'PYMODULE'),
  ('src.gui.drag_drop_frame',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\drag_drop_frame.py',
   'PYMODULE'),
  ('src.gui.enhanced_window',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\enhanced_window.py',
   'PYMODULE'),
  ('src.timestamp_processor',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\timestamp_processor.py',
   'PYMODULE'),
  ('src.video_processor',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\video_processor.py',
   'PYMODULE'),
  ('ssl', 'F:\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'F:\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'F:\\Python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'F:\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'F:\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'F:\\Python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'F:\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'F:\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'F:\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'F:\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('tifffile',
   'F:\\Python\\Lib\\site-packages\\tifffile\\__init__.py',
   'PYMODULE'),
  ('tifffile._imagecodecs',
   'F:\\Python\\Lib\\site-packages\\tifffile\\_imagecodecs.py',
   'PYMODULE'),
  ('tifffile.geodb',
   'F:\\Python\\Lib\\site-packages\\tifffile\\geodb.py',
   'PYMODULE'),
  ('tifffile.tifffile',
   'F:\\Python\\Lib\\site-packages\\tifffile\\tifffile.py',
   'PYMODULE'),
  ('tifffile.zarr',
   'F:\\Python\\Lib\\site-packages\\tifffile\\zarr.py',
   'PYMODULE'),
  ('tkinter', 'F:\\Python\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'F:\\Python\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'F:\\Python\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'F:\\Python\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'F:\\Python\\Lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.font', 'F:\\Python\\Lib\\tkinter\\font.py', 'PYMODULE'),
  ('tkinter.messagebox', 'F:\\Python\\Lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.scrolledtext',
   'F:\\Python\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'F:\\Python\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'F:\\Python\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinterdnd2',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\__init__.py',
   'PYMODULE'),
  ('tkinterdnd2.TkinterDnD',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\TkinterDnD.py',
   'PYMODULE'),
  ('token', 'F:\\Python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'F:\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'F:\\Python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'F:\\Python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'F:\\Python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'F:\\Python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tqdm', 'F:\\Python\\Lib\\site-packages\\tqdm\\__init__.py', 'PYMODULE'),
  ('tqdm._dist_ver',
   'F:\\Python\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'F:\\Python\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'F:\\Python\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli', 'F:\\Python\\Lib\\site-packages\\tqdm\\cli.py', 'PYMODULE'),
  ('tqdm.gui', 'F:\\Python\\Lib\\site-packages\\tqdm\\gui.py', 'PYMODULE'),
  ('tqdm.notebook',
   'F:\\Python\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std', 'F:\\Python\\Lib\\site-packages\\tqdm\\std.py', 'PYMODULE'),
  ('tqdm.utils', 'F:\\Python\\Lib\\site-packages\\tqdm\\utils.py', 'PYMODULE'),
  ('tqdm.version',
   'F:\\Python\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc', 'F:\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('ttkbootstrap',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.colorutils',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\colorutils.py',
   'PYMODULE'),
  ('ttkbootstrap.constants',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\constants.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\dialogs\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colorchooser',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colorchooser.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colordropper',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colordropper.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.dialogs',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\dialogs\\dialogs.py',
   'PYMODULE'),
  ('ttkbootstrap.icons',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\icons.py',
   'PYMODULE'),
  ('ttkbootstrap.localization',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\localization\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgcat',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\localization\\msgcat.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgs',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\localization\\msgs.py',
   'PYMODULE'),
  ('ttkbootstrap.publisher',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\publisher.py',
   'PYMODULE'),
  ('ttkbootstrap.style',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\style.py',
   'PYMODULE'),
  ('ttkbootstrap.themes',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\themes\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.standard',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\themes\\standard.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.user',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\themes\\user.py',
   'PYMODULE'),
  ('ttkbootstrap.tooltip',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\tooltip.py',
   'PYMODULE'),
  ('ttkbootstrap.utility',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\utility.py',
   'PYMODULE'),
  ('ttkbootstrap.validation',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\validation.py',
   'PYMODULE'),
  ('ttkbootstrap.widgets',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\widgets.py',
   'PYMODULE'),
  ('ttkbootstrap.window',
   'F:\\Python\\Lib\\site-packages\\ttkbootstrap\\window.py',
   'PYMODULE'),
  ('tty', 'F:\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'F:\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'F:\\Python\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'F:\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'F:\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'F:\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'F:\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'F:\\Python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'F:\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'F:\\Python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'F:\\Python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'F:\\Python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'F:\\Python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'F:\\Python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'F:\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'F:\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'F:\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'F:\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'F:\\Python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'F:\\Python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'F:\\Python\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'F:\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xlrd', 'F:\\Python\\Lib\\site-packages\\xlrd\\__init__.py', 'PYMODULE'),
  ('xlrd.biffh', 'F:\\Python\\Lib\\site-packages\\xlrd\\biffh.py', 'PYMODULE'),
  ('xlrd.book', 'F:\\Python\\Lib\\site-packages\\xlrd\\book.py', 'PYMODULE'),
  ('xlrd.compdoc',
   'F:\\Python\\Lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE'),
  ('xlrd.formatting',
   'F:\\Python\\Lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE'),
  ('xlrd.formula',
   'F:\\Python\\Lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE'),
  ('xlrd.info', 'F:\\Python\\Lib\\site-packages\\xlrd\\info.py', 'PYMODULE'),
  ('xlrd.sheet', 'F:\\Python\\Lib\\site-packages\\xlrd\\sheet.py', 'PYMODULE'),
  ('xlrd.timemachine',
   'F:\\Python\\Lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE'),
  ('xlrd.xldate',
   'F:\\Python\\Lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE'),
  ('xml', 'F:\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'F:\\Python\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'F:\\Python\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'F:\\Python\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'F:\\Python\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'F:\\Python\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'F:\\Python\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'F:\\Python\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'F:\\Python\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'F:\\Python\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'F:\\Python\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'F:\\Python\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'F:\\Python\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'F:\\Python\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'F:\\Python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'F:\\Python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'F:\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'F:\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'F:\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'F:\\Python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'F:\\Python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'F:\\Python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'F:\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'F:\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'F:\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'F:\\Python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'F:\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'F:\\Python\\Lib\\zipimport.py', 'PYMODULE')])
