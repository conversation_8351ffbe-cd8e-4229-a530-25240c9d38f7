#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复版程序是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_fixed_executable():
    """测试修复版可执行文件"""
    exe_path = Path("dist/VideoEditor_Fixed/VideoEditor_Fixed.exe")
    
    if not exe_path.exists():
        print("❌ 修复版可执行文件不存在")
        print("请先运行修复版打包脚本")
        return False
    
    print("✅ 修复版可执行文件存在")
    print(f"📁 文件路径: {exe_path.absolute()}")
    print(f"📊 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
    
    return True

def test_fixed_startup():
    """测试修复版程序启动"""
    exe_path = Path("dist/VideoEditor_Fixed/VideoEditor_Fixed.exe")
    
    print("🚀 测试修复版程序启动...")
    print("注意: 程序将在10秒后自动关闭")
    
    try:
        # 启动程序
        process = subprocess.Popen([str(exe_path)], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # 等待10秒
        time.sleep(10)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 修复版程序启动成功！")
            print("✅ 程序正在正常运行，没有闪退")
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            # 进程已经结束，可能有错误
            stdout, stderr = process.communicate()
            print("❌ 修复版程序仍然闪退")
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            if stdout:
                print(f"输出信息: {stdout.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def compare_versions():
    """比较原版和修复版"""
    original_path = Path("dist/卡妙视频编辑软件/卡妙视频编辑软件.exe")
    fixed_path = Path("dist/VideoEditor_Fixed/VideoEditor_Fixed.exe")
    
    print("\n📊 版本对比:")
    
    if original_path.exists():
        original_size = original_path.stat().st_size / 1024 / 1024
        print(f"原版大小: {original_size:.1f} MB")
    else:
        print("原版: 不存在")
    
    if fixed_path.exists():
        fixed_size = fixed_path.stat().st_size / 1024 / 1024
        print(f"修复版大小: {fixed_size:.1f} MB")
    else:
        print("修复版: 不存在")

def main():
    """主函数"""
    print("🧪 修复版程序测试工具")
    print("=" * 30)
    
    if not os.path.exists("dist/VideoEditor_Fixed"):
        print("❌ 未找到修复版输出目录")
        print("请先运行: python fix_build.py")
        print("或者双击: 修复闪退问题.bat")
        return
    
    print("开始测试修复版程序...")
    print()
    
    # 测试文件存在性
    if not test_fixed_executable():
        return
    
    # 测试启动
    startup_success = test_fixed_startup()
    
    # 版本对比
    compare_versions()
    
    print("\n" + "=" * 30)
    if startup_success:
        print("🎉 测试完成 - 修复版程序正常工作！")
        print("✅ 闪退问题已解决")
        print("🚀 可以将修复版部署到其他电脑使用")
    else:
        print("❌ 测试完成 - 修复版仍有问题")
        print("建议检查错误信息或联系技术支持")

if __name__ == "__main__":
    main()
